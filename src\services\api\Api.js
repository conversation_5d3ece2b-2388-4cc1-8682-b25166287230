import axios from 'axios';

const BASE_URL = 'https://backend.graycorp.io:9603/efly/api/hotelBooking';

const defaultHeaders = {
  accept: '*/*',
  'Content-Type': 'application/json',
};

const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: defaultHeaders,
  timeout: 30000,
});

const retryRequest = async (fn, retries = 3, delay = 1000) => {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === retries - 1) throw error;
      console.warn(`Retrying request (${i + 1}/${retries}) after error: ${error.message}`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
};

apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response [${response.config.url}]:`, JSON.stringify(response.data, null, 2));
    return response;
  },
  (error) => {
    const errorMessage = error.response
      ? error.response.data.message || error.response.data.validation_message || `HTTP error ${error.response.status}`
      : error.request
      ? 'No response received from server'
      : error.message || 'Request setup error';
    console.error(`API Error [${error.config?.url}]:`, errorMessage);
    return Promise.reject(new Error(errorMessage));
  }
);

export const fetchHotelIdsByCity = async (requestBody) => {
  console.log('fetchHotelIdsByCity Request:', JSON.stringify(requestBody, null, 2));
  const response = await retryRequest(() => apiClient.post('/searchhotels', requestBody));
  const data = response.data;
  if (data.validation_status !== 'Success') {
    throw new Error(data.validation_message || 'API validation failed');
  }
  console.log('fetchHotelIdsByCity completed successfully');
  return {
    hotels: Array.isArray(data.contents?.results) ? data.contents.results : [],
    totalHotels: data['Hotel Count :- '] || data.contents?.totalHotels || (Array.isArray(data.contents?.results) ? data.contents.results.length : 0),
  };
};

export const fetchFilterCounts = async (hotelIds) => {
  console.log('fetchFilterCounts Request:', JSON.stringify(hotelIds, null, 2));
  const response = await retryRequest(() => apiClient.post('/getHotelCountsByFacilities', hotelIds));
  console.log('fetchFilterCounts completed successfully');
  return Array.isArray(response.data) ? response.data : [];
};

export const fetchFilteredHotels = async (filterRequest) => {
  console.log('fetchFilteredHotels Request:', JSON.stringify(filterRequest, null, 2));
  const response = await retryRequest(() => apiClient.post('/filterAvailableHotels', filterRequest));
  console.log('fetchFilteredHotels completed successfully');
  const data = response.data;
  return {
    filteredHotels: Array.isArray(data.Results) ? data.Results : [],
    totalFilteredHotels: Array.isArray(data.Results) ? data.Results.length : 0,
  };
};

export const fetchHotelDetailsByHotelId = async (hotelId) => {
  console.log('fetchHotelDetailsByHotelId Request:', hotelId);
  const response = await retryRequest(() => apiClient.get(`/getHotelDetailsByHotelId/${hotelId}`));
  console.log('fetchHotelDetailsByHotelId completed successfully');
  return response.data;
};

export const fetchRooms = async (requestBody) => {
  console.log('fetchRooms Request:', JSON.stringify(requestBody, null, 2));
  const response = await retryRequest(() => apiClient.post('/getRooms', requestBody));
  console.log('fetchRooms completed successfully');
  return response.data;
};

export const getRoomsWithBlocking = async (requestBody) => {
  console.log('getRoomsWithBlocking Request:', JSON.stringify(requestBody, null, 2));
  const response = await retryRequest(() => apiClient.post('/getRoomsWithBlocking', requestBody));
  console.log('getRoomsWithBlocking completed successfully');
  return response.data;
};

export const saveBooking = async (requestBody) => {
  console.log('saveBooking Request:', JSON.stringify(requestBody, null, 2));
  const response = await retryRequest(() => apiClient.post('/savebooking', requestBody));
  console.log('saveBooking completed successfully');
  return response.data;
};

export const bookItineraryWithConfirmNo = async (requestBody) => {
  console.log('bookItineraryWithConfirmNo Request:', JSON.stringify(requestBody, null, 2));
  const response = await retryRequest(() => apiClient.post('/bookitineraryWithConfirmNo', requestBody));
  console.log('bookItineraryWithConfirmNo completed successfully');
  return response.data;
};

export const bookItineraryWithConfirmYes = async (requestBody) => {
  console.log('bookItineraryWithConfirmYes Request:', JSON.stringify(requestBody, null, 2));
  const response = await retryRequest(() => apiClient.post('/bookitineraryWithConfirmYes', requestBody));
  console.log('bookItineraryWithConfirmYes completed successfully');
  return response.data;
};