import React, { useRef, useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import ClimateRegion from "./ClimateRegion";
import Review from "./Review";
import Description from "./Description";
import AllOffer from "./AllOffer";

const tabs = [
  { id: "description", label: "Description" },
  { id: "allOffer", label: "All Offer" },
  { id: "review", label: "Review" },
  { id: "climateRegion", label: "Climate + Region" },
];

const Booking = ({ hotelDetails, searchCriteria }) => {
  const location = useLocation();
  const localSearchCriteria = location.state?.formData || {};

  const [activeTab, setActiveTab] = useState("allOffer");

  const sectionRefs = {
    description: useRef(null),
    allOffer: useRef(null),
    review: useRef(null),
    climateRegion: useRef(null),
  };

  const scrollToSection = (id) => {
    sectionRefs[id]?.current?.scrollIntoView({ behavior: "smooth", block: "start" });
    setActiveTab(id);
  };

  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: "0px 0px -60% 0px",
      threshold: 0,
    };

    const observerCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const id = entry.target.getAttribute("data-id");
          setActiveTab(id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    Object.keys(sectionRefs).forEach((key) => {
      const section = sectionRefs[key].current;
      if (section) observer.observe(section);
    });

    return () => observer.disconnect();
  }, []);

  return (
    <div className="flex flex-col space-y-6 ">
      <div className="px-6 py-3 md:px-0 md:py-0">
        <div className="border border-border rounded-[2px] px-1 h-auto w-full overflow-x-auto">
          <div className="flex flex-row items-center justify-start md:justify-between gap-2 min-w-[480px] md:min-w-0">
            {tabs.map((tab, index) => (
              <React.Fragment key={tab.id}>
                <div className="relative flex-1 min-w-[100px] h-full flex items-center justify-center">
                  <button
                    onClick={() => scrollToSection(tab.id)}
                    className={`w-full h-full md:py-4 py-3 px-2 text-sm sm:text-base transition-all duration-200 ${activeTab === tab.id
                        ? "text-darkBlue font-medium"
                        : "text-gray-500 hover:text-primaryColor"
                      }`}
                  >
                    {tab.label}
                  </button>
                  {activeTab === tab.id && (
                    <div className="absolute bottom-0 md:top-[53px] top-[55px] left-0 w-full h-[2px] bg-darkBlue rounded-[2px]" />
                  )}
                </div>
                {index < tabs.length - 1 && (
                  <div className="hidden sm:block h-6 w-[1px] bg-border" />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      <div className="space-y-10 scroll-smooth ">
        <div ref={sectionRefs.description} data-id="description">
          <Description hotelDetails={hotelDetails} />
        </div>
        <div ref={sectionRefs.allOffer} data-id="allOffer">
          <AllOffer hotelDetails={hotelDetails} searchCriteria={searchCriteria} />
        </div>
        <div ref={sectionRefs.review} data-id="review">
          <Review />
        </div>
        <div ref={sectionRefs.climateRegion} data-id="climateRegion">
          <ClimateRegion />
        </div>
      </div>
    </div>
  );
};

export default Booking;