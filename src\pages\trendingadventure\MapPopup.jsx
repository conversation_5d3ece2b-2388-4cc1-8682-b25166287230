import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";

// Fix Leaflet marker icon issue
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png",
  iconUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
  shadowUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png",
});

const MapModal = ({ isOpen, onClose, hotel }) => {
  if (!isOpen) return null;

  const lat = parseFloat(hotel.geoLat);
  const lng = parseFloat(hotel.geoLong);

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-[2px] p-6 w-full max-w-[600px] mx-4 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-darkBlue text-lg font-bold"
        >
          ✕
        </button>
        <h3 className="text-lg font-semibold text-darkBlue mb-4">
          {hotel.name} Location
          <p className="text-sm font-light text-darkBlue">{hotel.hotelStreetAddress}</p>
        </h3>
        
        {isNaN(lat) || isNaN(lng) ? (
          <div className="text-center text-red-500 h-[400px] flex items-center justify-center">
            Unable to display map: Invalid location data
          </div>
        ) : (
          <MapContainer
            center={[lat, lng]}
            zoom={15}
            style={{ height: "400px", width: "100%", borderRadius: "2px" }}
            className="border border-border"
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            />
            <Marker position={[lat, lng]}>
              <Popup>
                <div>
                  <h4 className="font-semibold text-darkBlue">{hotel.name}</h4>
                  <p className="text-sm text-gray-600">{hotel.hotelStreetAddress}</p>
                </div>
              </Popup>
            </Marker>
          </MapContainer>
        )}
      </div>
    </div>
  );
};

export default MapModal;