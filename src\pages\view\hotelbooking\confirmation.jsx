import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import { Download } from 'lucide-react';
import TrackingStepper from './TrackingStepper';
import ConfirmationCard from '../../../components/ui/ConfirmationCard';
import HotelDetails from './HotelDetails';
import { toast } from 'react-toastify';

// Format date helper
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-US', {
    weekday: 'short',
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  });
};

// Download PDF helper (placeholder)
const downloadPDF = (bookingDetails) => {
  toast.info('PDF download functionality is not implemented yet.');
};

// Parse confirmation details to extract voucher information
const parseVoucherDetails = (confirmationDetails, bookingDetails) => {
  const { guestDetails, searchCriteria, selectedRoom, hotelDetails } = bookingDetails || {};
  const { adults = 2, children = 0 } = searchCriteria || {};

  const parseCancellationRules = (rules) => {
    if (!rules || rules.length === 0) {
      return [{ text: 'No cancellation policy available.' }];
    }
    return rules.map((rule) => {
      if (rule.type === 'free_cancellation') {
        return {
          text: `Free Cancellation: ${rule.toDateDetails || 'Before check-in'}`,
        };
      } else if (rule.type === 'penalty_period') {
        return {
          text: `Cancellation Fee: ${rule.formattedCancelCharge || 'N/A'}`,
          subText: `From ${rule.fromDateDetails || 'N/A'}${rule.toDateDetails ? ` to ${rule.toDateDetails}` : ''}`,
        };
      } else if (rule.type === 'no_show') {
        return {
          text: `No Show Charge: ${rule.formattedCharge || rule.formattedCancelCharge || 'Full amount'}`,
        };
      }
      return {
        text: 'Unknown cancellation rule.',
      };
    });
  };

  const mainPassenger = guestDetails?.passengers?.[0] || {};

  return {
    bookingReferenceNo: confirmationDetails?.returnedCode || 'HTL-WBD-781230483',
    printedOn: formatDate(new Date()),
    itineraryNumber: confirmationDetails?.itineraryNumber || 'ITN-WBD-781230473',
    bookedBy: 'Efly AG',
    bookingStatus: confirmationDetails?.isSuccessful ? 'Confirmed' : 'Failed',
    supplierName: hotelDetails?.name || 'N/A',
    serviceName: hotelDetails?.name || 'N/A',
    address: `${hotelDetails?.address || ''}, ${hotelDetails?.city || ''}, ${hotelDetails?.country || ''}`,
    telephone: hotelDetails?.hotelPhone || '33-33-1-49197777',
    passengerName: mainPassenger
      ? `${mainPassenger.title || ''} ${mainPassenger.firstName || ''} ${mainPassenger.lastName || ''}`.trim()
      : 'N/A',
    passengerNationality: mainPassenger.nationality || 'N/A',
    countryOfResidence: mainPassenger.country || 'N/A',
    serviceType: 'Accommodation',
    city: hotelDetails?.city || 'N/A',
    supplierReference: confirmationDetails?.supplierReference || 'The supplier confirmation number will be visible 5 days before check-in.',
    checkIn: formatDate(searchCriteria?.dateRange?.startDate),
    checkOut: formatDate(searchCriteria?.dateRange?.endDate),
    roomType: selectedRoom?.roomTypeName || 'Standard Room',
    roomOccupancy: `${adults} Adults${children > 0 ? `, ${children} Children` : ''}`,
    rateBasis: selectedRoom?.rateBaseName || 'Room Only',
    additionalRequests: guestDetails?.additionalRequests || 'none',
    dailyRates: selectedRoom?.availableDates?.map((date) => ({
      date: date.day,
      payableRate: `${date.formatedPrice || date.price} ${selectedRoom?.currencyCode || 'USD'}`,
      rateBasis: selectedRoom?.rateBaseName || 'Room Only',
      rateMarket: hotelDetails?.regionName || 'EUROPE',
    })) || [],
    totalPayable: confirmationDetails?.totalPrice || selectedRoom?.totalCharge || 'N/A',
    currency: confirmationDetails?.currencyCode || selectedRoom?.currencyCode || 'USD',
    cancellationRules: parseCancellationRules(selectedRoom?.cancellationRules),
    tariffNotes: selectedRoom?.tariffNotes || 'No additional notes.',
  };
};

const Confirmation = () => {
  const { state } = useLocation();
  const { bookingDetails } = state || {};
  console.log('Confirmation - Received bookingDetails:', bookingDetails);

  if (!bookingDetails) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-offWhite">
        <div className="text-center text-gray-600">
          <h3 className="text-lg font-semibold">No Booking Details Available</h3>
          <p className="text-sm mt-2">Please complete the booking process to view your confirmation.</p>
          <Link to="/" className="mt-4 inline-block bg-darkBlue text-white py-2 px-4 rounded-[8px] hover:bg-darkBlue/90">
            Return to Home
          </Link>
        </div>
      </div>
    );
  }

  const { guestDetails = {}, selectedRoom = {}, hotelDetails = {}, nights = 1, bookingCode, confirmationDetails, selectedPaymentMethod } = bookingDetails;

  const reservationData = {
    code: bookingCode || 'N/A',
    issueDeadline: confirmationDetails?.issueDeadline || 'N/A',
    bookingCompletedAt: confirmationDetails?.bookingCompletedAt || new Date().toLocaleString(),
  };

  const invoiceAddress = [
    { label: 'First Name', value: guestDetails.passengers?.[0]?.firstName || 'N/A' },
    { label: 'Last Name', value: guestDetails.passengers?.[0]?.lastName || 'N/A' },
    { label: 'Email Address', value: guestDetails.passengers?.[0]?.email || 'N/A' },
    { label: 'Phone Number', value: guestDetails.passengers?.[0]?.phoneNumber ? `+${guestDetails.passengers[0].phoneCode}${guestDetails.passengers[0].phoneNumber}` : 'N/A' },
  ];

  const priceDetails = confirmationDetails
    ? [
        { label: 'Room Price', value: `${confirmationDetails.totalPrice || 'N/A'} ${confirmationDetails.currencyCode || 'CHF'}` },
        { label: 'Total Incl. Taxes', value: `${confirmationDetails.totalPrice || 'N/A'} ${confirmationDetails.currencyCode || 'CHF'}` },
        { label: 'Payment Method', value: selectedPaymentMethod || 'On account' },
      ]
    : [
        { label: 'Room Price', value: `CHF ${selectedRoom?.totalCharge || 0}` },
        { label: 'Total Incl. Taxes', value: `CHF ${(selectedRoom?.totalCharge || 0) * 1.137}` },
        { label: 'Payment Method', value: selectedPaymentMethod || 'On account' },
      ];

  const agencyInfo = {
    name: 'EFly AG',
    address: 'Longstrasse 214',
    city: 'CH 8005 Zurich',
    phone: '0041 44666 06 06',
    email: '<EMAIL>',
    description: 'EFly internet travel shop',
    subDescription: 'insurance package for',
    openingHours: {
      current: 'Open Now',
      closing: 'Close at 18:00',
      nextDay: 'Open on Friday 09:00 - 18:00',
    },
  };

  const voucherDetails = parseVoucherDetails(confirmationDetails, bookingDetails);
  console.log('Confirmation - Parsed voucherDetails:', voucherDetails);

  return (
    <div className="w-full mx-auto p-4 animate-fade-in">
      <TrackingStepper currentStep={3} />
      <ConfirmationCard
        title="Thank You for Booking!"
        reservationData={reservationData}
        invoiceAddress={invoiceAddress}
        priceDetails={priceDetails}
        agencyInfo={agencyInfo}
        bookingCompletedAt={reservationData.bookingCompletedAt}
        voucherDetails={voucherDetails}
        specificDetails={
          <div>
            <HotelDetails bookingDetails={bookingDetails} />
          </div>
        }
        showChangeCheckbox={true}
      />
      <div className="flex justify-between mt-6 w-full max-w-[80%]">
        <Link
          to="/"
          className="bg-gray-200 text-darkBlue py-2 px-4 rounded-[8px] hover:bg-gray-300"
        >
          Back to Home
        </Link>
        <Link
          to="/TrendingPackage"
          className="bg-darkBlue text-white py-2 px-4 rounded-[8px] hover:bg-darkBlue/90"
        >
          Book Another
        </Link>
      </div>
      <div className="flex justify-between items-center mb-6">
        <button
          type="button"
          className="flex items-center bg-darkBlue text-white py-2 px-4 rounded-[8px] hover:bg-darkBlue/90"
          onClick={() => downloadPDF(bookingDetails)}
        >
          <Download className="w-5 h-5 mr-2" />
          Download PDF
        </button>
      </div>
    </div>
  );
};

export default Confirmation;