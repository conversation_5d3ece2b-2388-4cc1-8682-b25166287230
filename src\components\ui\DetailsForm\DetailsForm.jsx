import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown } from 'lucide-react';
import { CountrySelect, StateSelect, CitySelect } from 'react-country-state-city';
import 'react-country-state-city/dist/react-country-state-city.css';
import './DetailsForm.css';
import DatePicker from 'react-date-picker';
import 'react-date-picker/dist/DatePicker.css';
import 'react-calendar/dist/Calendar.css';
import { Checkbox, FormControlLabel, Radio, RadioGroup, Grid } from '@mui/material';
import { debounce } from 'lodash';
import { getCountries, getCountryCallingCode } from 'libphonenumber-js';

// Reusable Input Component
const FormInput = ({
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  className = '',
  inputClassName = '',
  prefix = null,
  error,
  touched,
  name,
  ...props
}) => {
  const handleInputChange = (e) => {
    let newValue = e.target.value;

    if (name.includes('phoneNumber')) {
      newValue = newValue.replace(/[^0-9]/g, '');
      if (newValue.length > 15) {
        newValue = newValue.slice(0, 15);
      }
    }

    if (name.includes('email')) {
      newValue = newValue.replace(/[^a-zA-Z0-9._%+-@]/g, '');
    }

    const syntheticEvent = {
      ...e,
      target: {
        ...e.target,
        value: newValue,
        name: e.target.name
      }
    };
    onChange(syntheticEvent);
  };

  return (
    <div className={`border border-darkBlue rounded-2xl px-4 py-2 ${className}`}>
      <label className="text-xs text-black font-light">{label}</label>
      <div className="flex items-center mt-1">
        {prefix && <div className="flex items-center text-black mr-2">{prefix}</div>}
        <input
          type={type}
          value={value || ''}
          onChange={handleInputChange}
          className={`w-full text-black text-base focus:outline-none ${inputClassName}`}
          placeholder={placeholder}
          name={name}
          inputMode={name.includes('phoneNumber') ? 'numeric' : name.includes('email') ? 'text' : 'text'}
          pattern={
            name.includes('phoneNumber') ? '^[0-9]{7,15}$' :
              name.includes('email') ? '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$' :
                undefined
          }
          {...props}
        />
      </div>
      {touched && error && <div className="text-red text-sm mt-1">{error}</div>}
    </div>
  );
};

// Reusable Phone Country Code Dropdown
const PhoneCountryCodeSelect = ({ value, onChange, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  const countries = getCountries().map((country) => ({
    code: country,
    callingCode: getCountryCallingCode(country),
    name: new Intl.DisplayNames(['en'], { type: 'region' }).of(country),
  }));

  const filteredCountries = countries.filter(
    (country) =>
      country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      country.callingCode.includes(searchTerm)
  );

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={dropdownRef} className={`phone-code-container relative ${className}`}>
      <div
        className=" flex items-center cursor-pointer border-r border-darkBlue pr-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-black">+{value || 'Select'}</span>
        <ChevronDown className="w-4 h-4 text-black ml-1" />
      </div>
      {isOpen && (
        <div className=" absolute bg-white border border-darkBlue rounded-2xl shadow-lg z-10 w-64 mt-1 max-h-60 overflow-y-auto">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search country or code"
            className="w-full p-2 text-sm text-black focus:outline-none border-b border-darkBlue"
          />
          {filteredCountries.length > 0 ? (
            filteredCountries.map((country) => (
              <div
                key={country.code}
                className={` p-2 cursor-pointer hover:bg-gray-100 ${value === country.callingCode ? 'selected bg-gray-200' : ''}`}
                onClick={() => {
                  onChange(country.callingCode);
                  setIsOpen(false);
                  setSearchTerm('');
                }}
              >
                <span className="text-sm">{`${country.name} (+${country.callingCode})`}</span>
              </div>
            ))
          ) : (
            <div className=" p-2 text-sm text-gray-500">No countries found</div>
          )}
        </div>
      )}
    </div>
  );
};

// Reusable Dropdown Component for title
const FormDropdown = ({
  label,
  value,
  onChange,
  placeholder,
  options = [],
  searchTerm,
  onSearchChange,
  isOpen,
  onToggle,
  dropdownRef,
  renderOption,
  emptyMessage = 'No options found',
  className = '',
  disabled = false,
}) => (
  <div ref={dropdownRef} className={`relative border border-darkBlue rounded-2xl px-4 py-2 ${className}`}>
    <label className="text-xs text-black font-light">{label}</label>
    <div
      className={` flex justify-between items-center mt-1 ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
      onClick={disabled ? undefined : onToggle}
    >
      <input
        type="text"
        value={searchTerm}
        onChange={onSearchChange}
        className="w-full text-black text-base focus:outline-none"
        placeholder={placeholder}
        onClick={(e) => e.stopPropagation()}
        disabled={disabled}
      />
      <ChevronDown className="w-4 h-4 text-black" />
    </div>
    {isOpen && !disabled && (
      <div className="absolute bg-white border border-darkBlue rounded-2xl shadow-lg z-10 w-full mt-1 max-h-60 overflow-y-auto">
        {options.length > 0 ? (
          options.map((option, index) => (
            <div
              key={index}
              className={` p-3 cursor-pointer hover:bg-gray-100 ${value === option.salutationName ? 'selected bg-gray-200' : ''}`}
              onClick={() => onChange(option)}
            >
              {renderOption ? (
                renderOption(option)
              ) : (
                <div className="text-sm font-medium">{option.salutationName || option}</div>
              )}
            </div>
          ))
        ) : (
          <div className=" p-3 text-sm text-gray-500">{emptyMessage}</div>
        )}
      </div>
    )}
  </div>
);

// Reusable Checkbox Component
const FormCheckbox = ({ checked, onChange, label, className = '' }) => (
  <FormControlLabel
    control={
      <Checkbox
        checked={checked}
        onChange={onChange}
        sx={{ color: '#5A5A5A', '&.Mui-checked': { color: '#024575' } }}
      />
    }
    label={label}
    className={`text-smokyGray text-sm font-light mb-4 ${className}`}
  />
);

// Reusable Radio Group Component
const FormRadioGroup = ({
  label,
  value,
  onChange,
  options,
  name,
  row = false,
  className = '',
  optional = false,
}) => (
  <div className={`w-full py-2 space-y-2 ${className}`}>
    <h2 className="text-base font-medium text-smokyGray">
      {label} {optional && <span className="font-normal text-xs">(optional)</span>}
    </h2>
    <RadioGroup
      row={row}
      name={name}
      value={value}
      onChange={onChange}
      className={row ? 'flex gap-8' : 'space-y-2'}
    >
      {options.map(({ label: optionLabel, value: optionValue }) => (
        <FormControlLabel
          key={optionValue}
          value={optionValue}
          control={<Radio sx={{ color: '#5A5A5A', '&.Mui-checked': { color: '#024575' } }} />}
          label={<span className="text-sm font-light text-smokyGray">{optionLabel}</span>}
        />
      ))}
    </RadioGroup>
  </div>
);

// Reusable Date Picker Component
const FormDatePicker = ({ label, value, onChange, className = '' }) => (
  <div className={`border border-darkBlue rounded-2xl px-4 py-2 ${className}`}>
    <label className="text-xs text-black font-light">{label}</label>
    <DatePicker
      value={value}
      onChange={onChange}
      className="w-full text-black text-base focus:outline-none mt-1"
    />
  </div>
);

// Collapsible Section Component
const CollapsibleSection = ({ isOpen, children, className = '' }) => (
  <div
    className={`overflow-hidden transition-all duration-700 ease-in-out ${isOpen ? 'max-h-[1000px] mt-4 opacity-100' : 'max-h-0 mt-0 opacity-0'}`}
  >
    {children}
  </div>
);

const DetailsForm = ({
  onChange,
  showFields = {},
  defaultValues = {},
  title = 'Details',
  showCompanyToggle = false,
  showTermsCheckbox = false,
  showBookingFor = false,
  showTravelPurpose = false,
  formikProps = {},
}) => {
  const { values, errors, touched, handleChange, setFieldValue, passengerIndex } = formikProps;
  const [dropdownStates, setDropdownStates] = useState({
    showTitleDropdown: false,
    showGenderDropdown: false,
  });
  const [showCompanyFields, setShowCompanyFields] = useState(defaultValues.showCompanyFields || false);
  const dropdownRefs = {
    title: useRef(null),
    gender: useRef(null),
  };

  // Debounced search for title dropdown
  const debouncedSetTitleSearch = debounce((value) => {
    setFieldValue(`passengers[${passengerIndex}].title`, value);
  }, 300);

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      Object.entries(dropdownRefs).forEach(([key, ref]) => {
        if (ref.current && !ref.current.contains(event.target)) {
          const dropdownKey = `show${key.charAt(0).toUpperCase() + key.slice(1)}Dropdown`;
          setDropdownStates((prev) => ({ ...prev, [dropdownKey]: false }));
        }
      });
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle title selection
  const handleTitleSelect = (newTitle) => {
    setFieldValue(`passengers[${passengerIndex}].title`, newTitle.salutationName);
    setDropdownStates((prev) => ({ ...prev, showTitleDropdown: false }));
  };

  // Automatically set nationality when nationality is true
  useEffect(() => {
    const shouldSyncNationality =
      values.nationality &&
      values.country &&
      values.countryCode &&
      values.nationality !== values.country;

    if (shouldSyncNationality) {
      setFieldValue(`passengers[${passengerIndex}].nationality`, values.country);
      setFieldValue(`passengers[${passengerIndex}].nationalityCode`, values.countryCode);
    }
  }, [values.nationality, values.country, values.countryCode, values.nationality, passengerIndex, setFieldValue]);

  // Render option for title dropdown
  const renderSimpleOption = (option) => (
    <div className="p-3 cursor-pointer hover:bg-gray-100 flex items-center">
      <div className="text-sm font-medium">{option.salutationName}</div>
    </div>
  );

  // Salutation options (used as titles)
  const titleOptions = [
    { salutationId: "147", salutationName: "Mr." },
    { salutationId: "148", salutationName: "Ms." },
    { salutationId: "149", salutationName: "Mrs." },
    { salutationId: "15134", salutationName: "Miss" },
    { salutationId: "558", salutationName: "Dr." },
    { salutationId: "1671", salutationName: "Madame" },
    { salutationId: "1328", salutationName: "Sir" },
    { salutationId: "3801", salutationName: "Sir/Madam" },
    { salutationId: "14632", salutationName: "Child" },
    { salutationId: "9234", salutationName: "Messrs." },
    { salutationId: "74185", salutationName: "Monsieur" },
    { salutationId: "74195", salutationName: "Mademoiselle" },
  ];

  // Options arrays
  const bookingForOptions = [
    { label: 'I am the main guest', value: 'self' },
    { label: 'Booking is for someone else', value: 'other' },
  ];
  const travelPurposeOptions = [
    { label: 'Yes', value: 'Yes' },
    { label: 'No', value: 'No' },
  ];

  return (
    <div className="w-full mx-auto p-6 bg-white rounded-2xl border border-darkBlue font-inter space-y-4">
      <h2 className="font-medium text-lg text-black">{title}:</h2>

      {/* Company Toggle */}
      {showFields.companyToggle && (
        <FormCheckbox
          checked={showCompanyFields}
          onChange={(e) => setShowCompanyFields(e.target.checked)}
          label="Need an invoice for your company"
        />
      )}

      {/* Company Fields */}
      <CollapsibleSection isOpen={showCompanyFields}>
        {showFields.company && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <FormInput
              label="Company"
              value={values.company || ''}
              onChange={handleChange}
              placeholder="Company"
              name={`passengers[${passengerIndex}].company`}
            />
            {showFields.vatRegNo && (
              <FormInput
                label="VAT Reg No"
                value={values.vatRegNo || ''}
                onChange={handleChange}
                placeholder="VAT Registration Number"
                name={`passengers[${passengerIndex}].vatRegNo`}
              />
            )}
          </div>
        )}
      </CollapsibleSection>

      {/* Name Fields */}
      <Grid container columns={12} columnSpacing={2}>
        {showFields.title && (
          <Grid width={{ xs: '100%', lg: '16%' }}>
            <FormDropdown
              label="Title"
              searchTerm={values.title || 'Select here'}
              onSearchChange={(e) => debouncedSetTitleSearch(e.target.value)}
              placeholder="Select title"
              options={titleOptions}
              isOpen={dropdownStates.showTitleDropdown}
              onToggle={() =>
                setDropdownStates((prev) => ({
                  ...prev,
                  showTitleDropdown: !prev.showTitleDropdown,
                }))
              }
              onChange={handleTitleSelect}
              dropdownRef={dropdownRefs.title}
              renderOption={renderSimpleOption}
              emptyMessage="No titles found"
              value={values.title}
              className='no-border title-dropdown'
            />
          </Grid>
        )}

        {showFields.firstName && (
          <Grid width={{ xs: '100%', lg: '38.5%' }}>
            <FormInput
              label="First Name"
              value={values.firstName}
              onChange={handleChange}
              placeholder="First name"
              error={errors.firstName}
              touched={touched.firstName}
              name={`passengers[${passengerIndex}].firstName`}
            />
          </Grid>
        )}

        {showFields.lastName && (
          <Grid width={{ xs: '100%', lg: '38.5%' }}>
            <FormInput
              label="Last Name"
              value={values.lastName}
              onChange={handleChange}
              placeholder="Last name"
              error={errors.lastName}
              touched={touched.lastName}
              name={`passengers[${passengerIndex}].lastName`}
            />
          </Grid>
        )}
      </Grid>

      {/* Address Fields */}
      {(showFields.streetName || showFields.houseNumber) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
          {showFields.houseNumber && (
            <FormInput
              label="House Number"
              value={values.houseNumber}
              onChange={handleChange}
              placeholder="House number"
              error={errors.houseNumber}
              touched={touched.houseNumber}
              name={`passengers[${passengerIndex}].houseNumber`}
            />
          )}
          {showFields.streetName && (
            <FormInput
              label="Street Name"
              value={values.streetName}
              onChange={handleChange}
              placeholder="Street name"
              error={errors.streetName}
              touched={touched.streetName}
              name={`passengers[${passengerIndex}].streetName`}
            />
          )}
        </div>
      )}

      {/* Postal Code and Country */}
      {(showFields.postalCode || showFields.country) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
          {showFields.postalCode && (
            <FormInput
              label="Postal Code"
              value={values.postalCode}
              onChange={handleChange}
              placeholder="Postal code"
              error={errors.postalCode}
              touched={touched.postalCode}
              name={`passengers[${passengerIndex}].postalCode`}
            />
          )}
          {showFields.country && (
            <div className="border border-darkBlue rounded-2xl px-4 py-2">
              <label className="text-xs text-black font-light">Country</label>
              <CountrySelect
                className="w-full text-black text-base mt-1"
                placeHolder="Search country"
                showFlag={true}
                value={values.country ? { name: values.country, id: values.countryCode } : null}
                onChange={(country) => {
                  try {
                    const phoneCode = getCountryCallingCode(country.iso2);
                    setFieldValue(`passengers[${passengerIndex}].country`, country.name);
                    setFieldValue(`passengers[${passengerIndex}].countryCode`, country.id);
                    setFieldValue(`passengers[${passengerIndex}].state`, '');
                    setFieldValue(`passengers[${passengerIndex}].stateCode`, '');
                    setFieldValue(`passengers[${passengerIndex}].city`, '');
                    setFieldValue(`passengers[${passengerIndex}].phoneCode`, phoneCode);
                    if (values.nationality) {
                      setFieldValue(`passengers[${passengerIndex}].nationality`, country.name);
                      setFieldValue(`passengers[${passengerIndex}].nationalityCode`, country.id);
                    }
                  } catch (error) {
                    console.error('Error fetching phone code:', error);
                    setFieldValue(`passengers[${passengerIndex}].phoneCode`, '');
                  }
                }}
              />
            </div>
          )}
        </div>
      )}

      {/* State and City */}
      {(showFields.state || showFields.city) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
          {showFields.state && (
            <div className="border border-darkBlue rounded-2xl px-4 py-2">
              <label className="text-xs text-black font-light">State/Province</label>
              <StateSelect
                className="w-full text-black text-base mt-1"
                countryid={values.countryCode || null}
                placeHolder="Search state"
                value={values.state ? { name: values.state, id: values.stateCode } : null}
                onChange={(state) => {
                  setFieldValue(`passengers[${passengerIndex}].state`, state.name);
                  setFieldValue(`passengers[${passengerIndex}].stateCode`, state.id);
                  setFieldValue(`passengers[${passengerIndex}].city`, '');
                }}
                disabled={!values.countryCode}
              />
            </div>
          )}
          {showFields.city && (
            <div className="border border-darkBlue rounded-2xl px-4 py-2">
              <label className="text-xs text-black font-light">City</label>
              <CitySelect
                className="w-full text-black text-base mt-1"
                countryid={values.countryCode || null}
                stateid={values.stateCode || null}
                placeHolder="Search city"
                value={values.city ? { name: values.city, id: values.city } : null}
                onChange={(city) => {
                  setFieldValue(`passengers[${passengerIndex}].city`, city.name);
                }}
                disabled={!values.stateCode}
              />
            </div>
          )}
        </div>
      )}

      {/* Phone and Email */}
      {(showFields.phoneNumber || showFields.email) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {showFields.phoneNumber && (
            <div className="border border-darkBlue rounded-2xl px-4 py-1">
              <label className="text-xs text-black font-light">Phone Number</label>
              <div className="flex items-center">
                <PhoneCountryCodeSelect
                  value={values.phoneCode}
                  onChange={(phoneCode) => setFieldValue(`passengers[${passengerIndex}].phoneCode`, phoneCode)}
                  className="mr-2 phone-code-container"
                />
                <FormInput
                  type="tel"
                  inputMode="numeric"
                  pattern="^[0-9]{7,15}$"
                  value={values.phoneNumber}
                  onChange={handleChange}
                  placeholder="Phone number"
                  error={errors.phoneNumber}
                  touched={touched.phoneNumber}
                  name={`passengers[${passengerIndex}].phoneNumber`}
                  className="flex-1 no-border"
                />
              </div>
            </div>
          )}
          {showFields.email && (
            <FormInput
              label="Email Address"
              type="email"
              inputMode="email"
              pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
              value={values.email}
              onChange={handleChange}
              placeholder="Email"
              error={errors.email}
              touched={touched.email}
              name={`passengers[${passengerIndex}].email`}
            />
          )}
        </div>
      )}

      {/* Nationality */}
      {showFields.nationality && (
        <div className="border border-darkBlue rounded-2xl px-4 py-2 mt-4">
          <label className="text-xs text-black font-light">Nationality</label>
          <CountrySelect
            className="w-full text-black text-base mt-1"
            placeHolder="Search nationality"
            showFlag={true}
            value={values.nationality ? { name: values.nationality, id: values.nationalityCode } : null}
            onChange={(country) => {
              setFieldValue(`passengers[${passengerIndex}].nationality`, country.name);
              setFieldValue(`passengers[${passengerIndex}].nationalityCode`, country.id);
            }}
            disabled={values.nationality}
          />
        </div>
      )}

      {/* Same Nationality Checkbox */}
      {showFields.nationality && (
        <FormCheckbox
          checked={values.nationality || false}
          onChange={(e) => {
            const isChecked = e.target.checked;

            setFieldValue(`passengers[${passengerIndex}].nationality`, isChecked, true);
            setTimeout(() => {
              const country = formikProps?.values?.country;
              const countryCode = formikProps?.values?.countryCode;

              if (isChecked && country && countryCode) {
                setFieldValue(`passengers[${passengerIndex}].nationality`, country);
                setFieldValue(`passengers[${passengerIndex}].nationalityCode`, countryCode);
              }
            }, 0);
          }}
          label="Country and nationality are the same"
          className="mt-4"
        />
      )}

      {/* Terms Checkbox */}
      {showFields.termsChecked && (
        <FormCheckbox
          checked={values.termsChecked || false}
          onChange={(e) => setFieldValue(`passengers[${passengerIndex}].termsChecked`, e.target.checked)}
          label="Yes, I'd like free paperless confirmation (recommended)"
          className="mt-4"
        />
      )}

      {/* Booking For */}
      {showFields.bookingFor && (
        <FormRadioGroup
          label="Who are you booking for?"
          name={`passengers[${passengerIndex}].bookingFor`}
          value={values.bookingFor || ''}
          onChange={handleChange}
          options={bookingForOptions}
          optional={true}
          className="border-t border-darkBlue mt-4"
        />
      )}

      {/* Travel Purpose */}
      {showFields.travelPurpose && (
        <FormRadioGroup
          label="Are you travelling for work?"
          name={`passengers[${passengerIndex}].travelPurpose`}
          value={values.travelPurpose || ''}
          onChange={handleChange}
          options={travelPurposeOptions}
          row={true}
          optional={true}
        />
      )}
    </div>
  );
};

export default DetailsForm;