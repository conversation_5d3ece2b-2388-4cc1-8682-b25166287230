import React, { useState, useEffect, useRef } from "react";
import { BedDouble, Calendar, Check, Minus, Plus, UserRound, Star } from "lucide-react";
import { useNavigate } from "react-router-dom";
import FacilitiesIcon from "../FacilitiesIcon";
import HotelCalendarComponent from "../../home/<USER>/HotelCalendarComponent";
import ButtonCom from "../../../components/ui/button/ButtonCom";

// Room Tabs
const RoomTabs = ({ roomTypes, selectedHeader, setSelectedHeader, scrollToRoom }) => (
  <div className="flex text-xs font-light space-x-4 mb-4 overflow-x-auto px-6 md:px-0 md:py-0">
    {roomTypes.map((roomType) => (
      <div
        key={roomType.id}
        className={`px-4 py-2 rounded-[8px] cursor-pointer transition-colors duration-200 whitespace-nowrap ${selectedHeader === roomType.id
          ? "bg-darkBlue text-white"
          : "bg-darkBlue/5 text-darkBlue hover:bg-darkBlue/10"
          }`}
        onClick={() => {
          setSelectedHeader(roomType.id);
          scrollToRoom(roomType.id);
        }}
        role="tab"
        aria-selected={selectedHeader === roomType.id}
        aria-label={`Scroll to ${roomType.type}`}
      >
        {roomType.type} ({roomType.configurations.length} options)
      </div>
    ))}
  </div>
);

// Room Listing
const RoomListing = ({
  roomType,
  config,
  configIndex,
  roomTypeIndex,
  selectedRooms,
  handleRoomSelect,
  travelerConfig,
  hotelDescription2,
  hotelDetails,
}) => {
  const [showFullNotes, setShowFullNotes] = useState(false);
  const [showAllFacilities, setShowAllFacilities] = useState(false);

  const toggleNotes = () => {
    setShowFullNotes((prev) => !prev);
  };

  const toggleFacilities = () => {
    setShowAllFacilities((prev) => !prev);
  };

  const displayedFacilities = showAllFacilities
    ? hotelDetails?.facilities || []
    : (hotelDetails?.facilities || []).slice(0, 5);

  return (
    <div
      key={config.id}
      className="border border-borderGray rounded-[8px] bg-white shadow-sm mb-4"
      id={`room-${roomType.id}`}
    >
      <div className="flex flex-col md:flex-row w-full">
        <div className="p-4 md:w-2/5 w-full space-y-2">
          <h3 className="text-lg text-darkBlue font-semibold underline">
            {roomType.type}
          </h3>
          <p className="text-sm flex items-center gap-2">
            {roomType.singleRoom} {roomType.singleRoom === 2 ? "Single" : "Double"}{" "}
            room
            {roomType.singleRoom === 2 && (
              <>
                <BedDouble className="w-4 h-4 ml-2 text-black" strokeWidth={1} />
                <BedDouble className="w-4 h-4 ml-2 text-black" strokeWidth={1} />
              </>
            )}{" "}
            /
          </p>
          {roomType.description && (
            <p className="text-sm">{roomType.description}</p>
          )}
          <div className="flex mt-1">
            {Array(travelerConfig.adults)
              .fill(0)
              .map((_, i) => (
                <UserRound key={`adult-${i}`} className="w-5 h-5 mr-1" />
              ))}
            {travelerConfig.children > 0 &&
              Array(travelerConfig.children)
                .fill(0)
                .map((_, i) => (
                  <UserRound key={`child-${i}`} className="w-4 h-4 mr-1" />
                ))}
          </div>
          {config.benefits.map((benefit, i) => (
            <div key={i} className="flex items-center mb-2">
              <Check className="text-darkGreen mr-1" size={16} />
              <span className="text-darkGreen font-semibold text-xs">
                {benefit.text}
              </span>
              {benefit.condition && (
                <span className="ml-2 text-darkGreen text-[10px]">
                  {benefit.condition}
                </span>
              )}
              {benefit.included && (
                <span className="ml-2 text-darkGreen text-[10px]">Included</span>
              )}
            </div>
          ))}
        </div>
        <div className="p-4 md:w-3/5 w-full space-y-3">
          {hotelDetails?.facilities?.length > 0 && (
            <div className="flex gap-x-5 gap-y-1 flex-wrap">
              {displayedFacilities.map((facility, i) => (
                <div key={`facility-${i}`} className="flex items-center font-light text-xs">
                  <FacilitiesIcon label={facility.label} className="w-3 h-4 mr-1" />
                  <span className="text-xs">{facility.label}</span>
                </div>
              ))}
              {hotelDetails.facilities.length > 5 && (
                <button
                  onClick={toggleFacilities}
                  className="text-darkBlue text-xs mt-1 hover:underline"
                >
                  {showAllFacilities ? "See Less" : "See More"}
                </button>
              )}
            </div>
          )}
          {roomTypeIndex === 0 && hotelDescription2 && (
            <div className="text-xs font-light whitespace-break-spaces relative">
              <div className={`${showFullNotes ? '' : 'line-clamp-3'}`}>
                {hotelDescription2}
              </div>
              {hotelDescription2.length > 300 && (
                <button
                  onClick={toggleNotes}
                  className="text-darkBlue text-xs mt-1 hover:underline"
                >
                  {showFullNotes ? 'Show Less' : 'Read More'}
                </button>
              )}
            </div>
          )}
        </div>
      </div>
      <div className="px-4 py-3 bg-smokeGray/20 flex items-center space-x-6">
        <div className="w-full justify-end text-end">
          <div className="text-xs">{config.nights}</div>
          <div className="text-base font-semibold">{config.price}</div>
          <div className="text-xs">{config.totalTax}</div>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={() =>
              handleRoomSelect(
                roomTypeIndex,
                configIndex,
                (selectedRooms[config.id] || 0) - 1
              )
            }
            disabled={(selectedRooms[config.id] || 0) <= 0}
            className="w-8 h-8 flex items-center justify-center bg-indigo-100 rounded-[8px] text-lg font-bold disabled:opacity-50"
            aria-label="Decrease room quantity"
          >
            -
          </button>
          <div className="w-8 h-8 flex items-center justify-center border border-border rounded-[8px] px-6">
            {selectedRooms[config.id] || 0}
          </div>
          <button
            onClick={() =>
              handleRoomSelect(
                roomTypeIndex,
                configIndex,
                (selectedRooms[config.id] || 0) + 1
              )
            }
            disabled={(selectedRooms[config.id] || 0) >= travelerConfig.rooms}
            className="w-8 h-8 flex items-center justify-center bg-indigo-100 rounded-[8px] text-lg font-bold disabled:opacity-50"
            aria-label="Increase room quantity"
          >
            +
          </button>
        </div>
      </div>
    </div>
  );
};

// Total Panel
const TotalPanel = ({
  travelerConfig,
  displayText,
  calculateTotalPrice,
  roomTypes,
  selectedRooms,
  handleReserve,
  bookingDetails,
}) => {
  const selectedRoomDetails = Object.entries(selectedRooms)
    .map(([roomId, count]) => {
      const [roomTypeIndex, configIndex] = roomId.split("-").map(Number);
      const roomType = roomTypes[roomTypeIndex];
      const config = roomType?.configurations[configIndex];
      if (!roomType || !config) return null;
      return {
        roomTypeName: roomType.type,
        config,
        count,
        roomId,
        roomTypeCode: roomType.roomTypeCode,
      };
    })
    .filter(Boolean);

  return (
    <div className="w-full md:w-1/4 md:block hidden">
      <div className="bg-smokeGray/10 border border-border rounded-[8px] p-4 space-y-4 shadow-sm h-auto">
        <h3 className="text-2xl font-bold">Total</h3>
        <div className="text-lg font-medium">
          CHF {calculateTotalPrice()}
          {selectedRoomDetails.length > 0 && (
            <p className="text-sm font-normal">
              {selectedRoomDetails[0].config.totalTax || "+ Taxes and Charges"}
            </p>
          )}
        </div>
        <div className="mb-2">
          <div className="text-sm">
            {travelerConfig.rooms} Room{travelerConfig.rooms > 1 ? "s" : ""} for
          </div>
          <div className="text-sm">{displayText}</div>
        </div>
        {selectedRoomDetails.length === 0 ? (
          <div className="text-sm text-gray-600">No rooms selected</div>
        ) : (
          selectedRoomDetails.map(({ roomTypeName, config, count, roomId, roomTypeCode }) => (
            <div key={roomId} className="space-y-2">
              <div className="text-sm font-semibold">
                {roomTypeName} ({count} {count > 1 ? "Rooms" : "Room"})
              </div>
              {config.benefits.map((benefit, i) => (
                <div key={i} className="flex items-start">
                  <Check className="text-darkGreen mr-2 mt-1" size={16} />
                  <div>
                    <span className="text-darkGreen font-semibold text-xs">
                      {benefit.text}
                    </span>
                    {benefit.condition && (
                      <span className="text-[10px] text-darkGreen">
                        {" "}{benefit.condition}
                      </span>
                    )}
                    {benefit.included && (
                      <span className="text-[10px] text-darkGreen"> Included</span>
                    )}
                  </div>
                </div>
              ))}
              <div className="text-sm">{config.nights}</div>
              <div className="text-base font-semibold">{config.price}</div>
              <div className="text-xs">{config.totalTax}</div>
            </div>
          ))
        )}
        <ButtonCom
          variant="primary"
          size="md"
          width="full"
          disabled={selectedRoomDetails.length === 0}
          onClick={() => {
            if (selectedRoomDetails.length > 0) {
              handleReserve(
                selectedRoomDetails[0].roomTypeName,
                selectedRoomDetails[0].config,
                selectedRoomDetails[0].roomTypeCode
              );
            }
          }}
          ariaLabel="Reserve selected room"
        >
          I'll Reserve
        </ButtonCom>
      </div>
    </div>
  );
};

// Main Component
export default function AllOffer({ hotelDetails, searchCriteria }) {
  const navigate = useNavigate();
  const calendarRef = useRef(null);
  const buttonRef = useRef(null);
  const modalRef = useRef(null);
  const roomRefs = useRef({});

  // Star rating calculation (same as WellcomSection.jsx)
  const getStarCount = () => {
    if (hotelDetails.classificationBody) {
      const starsFromName = hotelDetails.classificationBody.classificationName?.match(/\*+/)?.[0]?.length;
      if (starsFromName) return starsFromName;

      const starsFromRating = hotelDetails.classificationBody.rating?.match(/(\d+)\s*Star/i)?.[1];
      if (starsFromRating) return parseInt(starsFromRating, 10);

      const classificationId = hotelDetails.classificationBody.classificationId;
      if (classificationId) {
        switch (classificationId) {
          case 559: return 1;
          case 560: return 2;
          case 561: return 3;
          case 562: return 4;
          case 563: return 5;
          default: return 0;
        }
      }
    }

    if (hotelDetails.classificationCode) {
      switch (hotelDetails.classificationCode) {
        case 559: return 1;
        case 560: return 2;
        case 561: return 3;
        case 562: return 4;
        case 563: return 5;
        default: return 0;
      }
    }

    return 0;
  };

  const getClassificationName = () => {
    if (hotelDetails.classificationBody?.classificationName) {
      return hotelDetails.classificationBody.classificationName.replace(/\*+$/, '').trim();
    }
    if (hotelDetails.classificationBody?.rating) {
      return hotelDetails.classificationBody.rating;
    }
    return hotelDetails.classificationName || 'Unrated';
  };

  const starCount = getStarCount();
  const classificationName = getClassificationName();

  //  starCount and classificationName
  const processedHotelDetails = {
    ...hotelDetails,
    starCount,
    classificationName,
  };

  const [selectedHeader, setSelectedHeader] = useState(null);
  const [selectedRooms, setSelectedRooms] = useState({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [activeSection, setActiveSection] = useState(null);
  const [showTravelersModal, setShowTravelersModal] = useState(false);
  const [showRoomSelectorId, setShowRoomSelectorId] = useState(null);
  const [travelerConfig, setTravelerConfig] = useState({
    rooms: searchCriteria?.rooms || 1,
    adults: searchCriteria?.adults || 2,
    children: searchCriteria?.children || 0,
  });
  const [dateRange, setDateRange] = useState({
    startDate: searchCriteria?.dateRange?.startDate || new Date("2025-05-25"),
    endDate: searchCriteria?.dateRange?.endDate || new Date("2025-05-28"),
    key: "selection",
  });
  const [flexibleDays, setFlexibleDays] = useState(0);

  const calculateNightsAndTravelers = () => {
    let nights = 1;
    let dateRangeText = "Select dates";
    try {
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
        if (endDate >= startDate) {
          nights = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
          const formatter = new Intl.DateTimeFormat("en-US", {
            month: "short",
            day: "2-digit",
            year: "numeric",
          });
          dateRangeText = `${formatter.format(startDate)} - ${formatter.format(endDate)}`;
        } else {
          console.warn("Invalid date range: endDate is before startDate");
          dateRangeText = "Invalid dates";
        }
      }
    } catch (error) {
      console.error("Error calculating nights:", error);
    }
    const adults = travelerConfig.adults;
    const children = travelerConfig.children;
    const travelersText = [
      adults > 0 ? `${adults} ${adults === 1 ? "Adult" : "Adults"}` : "",
      children > 0 ? `${children} ${children === 1 ? "Child" : "Children"}` : "",
    ]
      .filter(Boolean)
      .join(", ");
    return {
      nights: Math.max(1, nights),
      dateRangeText,
      displayText: `${nights} ${nights === 1 ? "night" : "nights"}, ${travelersText}`,
    };
  };

  const { nights, dateRangeText, displayText } = calculateNightsAndTravelers();

  const processRoomTypes = (nights) => {
    if (!hotelDetails?.rooms || hotelDetails.rooms.length === 0) {
      console.warn("No rooms available in hotelDetails:", hotelDetails);
      return [];
    }

    return hotelDetails.rooms.map((roomType, index) => ({
      id: roomType.roomTypeCode || index,
      type: roomType.roomTypeName || "Unknown Room Type",
      singleRoom: roomType.twin === "yes" ? 2 : 1,
      description: roomType.roomInfo
        ? `Max ${roomType.roomInfo.maxOccupancy} guests`
        : "No description available.",
      roomTypeCode: roomType.roomTypeCode || `ROOM_${index}`,
      configurations: roomType.rateBases?.map((rate, configIndex) => {
        const mealPlans = rate.availableDates?.flatMap((date) =>
          date.mealIncludes?.map((meal) => meal.mealName || meal.mealType) || []
        ) || [];
        const uniqueMealPlans = [...new Set(mealPlans)];

        const cancellationBenefits = rate.cancellationRules?.map((rule) => {
          if (rule.type === "free_cancellation" && rule.cancelCharge === 0) {
            return {
              text: "Free Cancellation",
              condition: rule.toDateDetails || "Before check-in",
              included: true,
            };
          } else if (rule.type === "penalty_period" && rule.cancelCharge > 0) {
            return {
              text: `Cancellation CHF ${rule.formattedCancelCharge}`,
              condition: rule.fromDateDetails
                ? `From ${rule.fromDateDetails}${rule.toDateDetails ? ` to ${rule.toDateDetails}` : ''}`
                : "During penalty period",
              included: false,
            };
          } else if (rule.type === "no_show" && rule.noShowPolicy) {
            return {
              text: `No-Show Fee: CHF ${rule.formattedCharge}`,
              condition: rule.fromDateDetails || "On check-in date",
              included: false,
            };
          }
          return null;
        }).filter(Boolean) || [];

        return {
          id: `${index}-${configIndex}`,
          guestConfig: {
            adults: travelerConfig.adults,
            children: travelerConfig.children,
            childAge: 6,
          },
          price: rate.totalCharge ? `CHF ${rate.totalCharge.toFixed(2)}` : "N/A",
          priceDetails: "+ CHF 4355 Taxes and Charges",
          benefits: [
            {
              text: rate.rateBaseName || "Room Only",
              included: !!rate.rateBaseName,
            },
            ...cancellationBenefits,
          ],
          nights: `${nights} ${nights === 1 ? "night" : "nights"}`,
          totalPrice: rate.totalCharge ? `CHF ${rate.totalCharge.toFixed(2)}` : "N/A",
          totalTax: "+ CHF 4355 Taxes and Charges",
          leftToSell: rate.leftToSell || 3,
          rateBaseId: rate.rateBaseId || 0,
          tariffNotes: rate.tariffNotes || "",
          allocationDetails: rate.allocationDetails || "",
          cancellationRules: rate.cancellationRules || [],
          availableDates: rate.availableDates || [],
        };
      }) || [],
    }));
  };

  const roomTypes = processRoomTypes(nights);

  const calculateTotalPrice = () => {
    if (!roomTypes || !Array.isArray(roomTypes)) {
      console.warn("roomTypes is not defined or not an array:", roomTypes);
      return "0.00";
    }
    let total = 0;
    Object.entries(selectedRooms).forEach(([roomId, count]) => {
      const [roomTypeIndex, configIndex] = roomId.split("-").map(Number);
      const config = roomTypes[roomTypeIndex]?.configurations[configIndex];
      if (config && config.totalPrice !== "N/A") {
        const price = parseFloat(config.totalPrice.replace("CHF ", ""));
        total += price * count;
      }
    });
    return total.toFixed(2);
  };

  const toggleCalendar = () => {
    setShowDatePicker((prev) => !prev);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setShowDatePicker(false);
      }
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setShowTravelersModal(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    setTravelerConfig({
      rooms: searchCriteria?.rooms || 1,
      adults: searchCriteria?.adults || 2,
      children: searchCriteria?.children || 0,
    });
    setDateRange({
      startDate: searchCriteria?.dateRange?.startDate || new Date("2025-05-25"),
      endDate: searchCriteria?.dateRange?.endDate || new Date("2025-05-28"),
      key: "selection",
    });
    setFlexibleDays(searchCriteria?.flexibleDays || 0);
  }, [searchCriteria]);

  useEffect(() => {
    setSelectedRooms({});
  }, [hotelDetails]);

  const getSectionBorderStyle = (sectionName) =>
    activeSection === sectionName
      ? "border border-gray/35"
      : "border border-transparent";

  const handleRoomSelect = (roomTypeIndex, configIndex, newCount) => {
    const roomId = `${roomTypeIndex}-${configIndex}`;
    setSelectedRooms((prev) => {
      const updated = { ...prev };
      if (newCount <= 0) {
        delete updated[roomId];
      } else if (newCount <= travelerConfig.rooms) {
        updated[roomId] = newCount;
      }
      return updated;
    });
  };

  const handleTravelerChange = (field, value) => {
    setTravelerConfig((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleConfirmTravelers = () => {
    console.log("Traveler Selection:", travelerConfig);
    setShowTravelersModal(false);
  };

  const handleReserve = (roomTypeName, config, roomTypeCode) => {
    const totalPrice = calculateTotalPrice();
    if (!totalPrice || totalPrice === "0.00") {
      alert("Please select a valid room with pricing.");
      return;
    }
    const selectedRoom = {
      roomTypeName,
      totalCharge: parseFloat(totalPrice) || 0,
      rateBaseId: config.rateBaseId || 0,
      roomTypeCode: roomTypeCode,
      allocationDetails: config.allocationDetails || "",
      isBookable: config.isBookable !== undefined ? config.isBookable : true,
      benefits: config.benefits || [],
      tariffNotes: config.tariffNotes || "",
      selectedRoomCounts: selectedRooms,
      cancellationRules: config.cancellationRules || [],
      availableDates: config.availableDates || [],
      rateBaseName: config.rateBaseName || 'Room Only',
      currencyCode: config.currencyCode || 'USD',
    };
    const bookingDetails = {
      hotelDetails: processedHotelDetails,
      searchCriteria: {
        ...searchCriteria,
        dateRange,
        adults: travelerConfig.adults,
        children: travelerConfig.children,
        rooms: travelerConfig.rooms,
      },
      selectedRoom,
      nights,
      dateRangeText,
    };
    console.log('AllOffer - bookingDetails:', bookingDetails);
    navigate("/finaldetails", { state: { bookingDetails } });
  };

  const scrollToRoom = (roomId) => {
    setTimeout(() => {
      const roomElement = roomRefs.current[roomId];
      if (roomElement) {
        roomElement.scrollIntoView({
          behavior: "smooth",
          block: "start",
          inline: "nearest",
        });
        setSelectedHeader(roomId);
      } else {
        console.warn(`Room element with ID room-${roomId} not found`);
      }
    }, 50);
  };

  const handleChangeSearch = () => {
    const updatedSearchCriteria = {
      ...searchCriteria,
      dateRange: {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      },
      adults: travelerConfig.adults,
      children: travelerConfig.children,
      rooms: travelerConfig.rooms,
      flexibleDays,
    };
    navigate(`/hotel-and-flight/${hotelDetails.id}`, {
      state: {
        formData: updatedSearchCriteria,
        previousSearch: searchCriteria,
      },
    });
  };

  const selectedRoomDetails = Object.entries(selectedRooms)
    .map(([roomId, count]) => {
      const [roomTypeIndex, configIndex] = roomId.split("-").map(Number);
      const roomType = roomTypes[roomTypeIndex];
      const config = roomType?.configurations[configIndex];
      if (!roomType || !config) return null;
      return {
        roomTypeName: roomType.type,
        config,
        count,
        roomId,
        roomTypeId: roomType.id,
        roomTypeCode: roomType.roomTypeCode,
      };
    })
    .filter(Boolean);

  if (!hotelDetails || !hotelDetails.rooms) {
    console.warn("hotelDetails or hotelDetails.rooms is undefined:", hotelDetails);
    return (
      <div className="text-center text-gray-600">
        Loading hotel details or no rooms available...
      </div>
    );
  }

  return (
    <div className="w-full space-y-0 md:space-y-8">
      <div className="mb-8 px-6 py-3 md:px-0 md:py-0">
        <h2 className="text-2xl font-semibold text-darkBlue mb-6">Availability</h2>
        {/* Add Star Rating Display */}
        {/* <div className="flex items-center mb-4">
          {starCount === 0 ? (
            <span className="text-sm text-darkBlue font-medium">{classificationName}</span>
          ) : (
            [...Array(5)].map((_, i) => (
              <Star
                key={i}
                className="w-4 h-4"
                fill={i < starCount ? "orange" : "none"}
                stroke={i < starCount ? "none" : "orange"}
              />
            ))
          )}
        </div> */}
        <div className="flex flex-col md:flex-row flex-wrap gap-2 justify-start md:justify-between p-2 rounded-[8px] border border-border max-w-[820px] w-full">
          <div className="flex flex-col md:flex-row md:w-[70%] border border-border bg-white items-center rounded-[8px] justify-between">
            <div
              className="flex flex-col relative w-full"
              onClick={() => setActiveSection("DateRange")}
            >
              <div
                className={`flex justify-between items-center w-full py-[15px] rounded-[8px] ${getSectionBorderStyle(
                  "DateRange"
                )}`}
              >
                <div className="hidden md:block absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
                <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                  <Calendar
                    className="text-smokyGray w-6 h-6 flex-shrink-0"
                    strokeWidth={1}
                  />
                  <div className="flex flex-col w-full">
                    <button
                      type="button"
                      ref={buttonRef}
                      onClick={toggleCalendar}
                      className="text-sm font-light text-black focus:outline-none text-left w-full"
                      aria-label="Select dates"
                    >
                      {dateRange?.startDate && dateRange?.endDate
                        ? `${new Date(dateRange.startDate).toLocaleDateString(
                          "en-US",
                          { month: "short", day: "2-digit", year: "numeric" }
                        )} - ${new Date(dateRange.endDate).toLocaleDateString(
                          "en-US",
                          { month: "short", day: "2-digit", year: "numeric" }
                        )}`
                        : "Select dates"}
                    </button>
                  </div>
                </div>
                {showDatePicker && (
                  <div
                    ref={calendarRef}
                    className="absolute z-50 mt-12 left-0 md:-left-12 shadow-lg w-full md:w-auto transform -translate-x-0 md:-translate-x-1/4"
                  >
                    <HotelCalendarComponent
                      dateRange={dateRange}
                      setDateRange={setDateRange}
                      onClose={() => setShowDatePicker(false)}
                      flexibleDays={flexibleDays}
                      setFlexibleDays={setFlexibleDays}
                    />
                  </div>
                )}
              </div>
            </div>
            <div
              className="flex flex-col relative w-full"
              onClick={() => setActiveSection("Travelers")}
            >
              <div
                className={`flex justify-between items-center w-full py-[15px] rounded-[8px] ${getSectionBorderStyle(
                  "Travelers"
                )}`}
                onClick={() => setShowTravelersModal(true)}
              >
                <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                  <UserRound
                    className="text-smokyGray w-6 h-6 flex-shrink-0"
                    strokeWidth={1}
                  />
                  <div className="flex flex-col w-full">
                    <span className="text-sm font-light text-black focus:outline-none">
                      {travelerConfig.adults} Adults {travelerConfig.children}{" "}
                      Children · {travelerConfig.rooms} Room
                    </span>
                  </div>
                </div>
                {showTravelersModal && (
                  <div
                    className="absolute z-50 mt-16 right-0 sm:left-auto bg-white border border-border shadow-lg p-4 rounded-[8px] w-[280px]"
                    ref={modalRef}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <div className="flex flex-col space-y-4">
                      <span className="flex justify-center text-base text-black border-b border-border pb-4">
                        Rooms & Travelers
                      </span>
                      <div className="flex items-center justify-between">
                        <button
                          type="button"
                          onClick={() =>
                            handleTravelerChange(
                              "rooms",
                              Math.max(1, travelerConfig.rooms - 1)
                            )
                          }
                          disabled={travelerConfig.rooms <= 1}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.rooms <= 1
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Decrease rooms"
                        >
                          <Minus className="w-3 h-3" />
                        </button>
                        <div className="text-center">
                          <span className="text-sm font-light text-darkGray">
                            {travelerConfig.rooms} Room
                          </span>
                        </div>
                        <button
                          type="button"
                          onClick={() =>
                            handleTravelerChange(
                              "rooms",
                              Math.min(5, travelerConfig.rooms + 1)
                            )
                          }
                          disabled={travelerConfig.rooms >= 5}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.rooms >= 5
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Increase rooms"
                        >
                          <Plus className="w-3 h-3" />
                        </button>
                      </div>
                      <div className="flex items-center justify-between">
                        <button
                          type="button"
                          onClick={() =>
                            handleTravelerChange(
                              "adults",
                              Math.max(1, travelerConfig.adults - 1)
                            )
                          }
                          disabled={travelerConfig.adults <= 1}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.adults <= 1
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Decrease adults"
                        >
                          <Minus className="w-3 h-3" />
                        </button>
                        <div className="text-center">
                          <span className="text-sm font-light text-darkGray">
                            {travelerConfig.adults} Adult
                          </span>
                          <div className="text-smokyGray text-[10px] font-light">
                            Ages 12+
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            const total = travelerConfig.adults + travelerConfig.children;
                            if (total < 9)
                              handleTravelerChange("adults", travelerConfig.adults + 1);
                          }}
                          disabled={travelerConfig.adults + travelerConfig.children >= 9}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.adults + travelerConfig.children >= 9
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Increase adults"
                        >
                          <Plus className="w-3 h-3" />
                        </button>
                      </div>
                      <div className="flex items-center justify-between">
                        <button
                          type="button"
                          onClick={() =>
                            handleTravelerChange(
                              "children",
                              Math.max(0, travelerConfig.children - 1)
                            )
                          }
                          disabled={travelerConfig.children <= 0}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.children <= 0
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Decrease children"
                        >
                          <Minus className="w-3 h-3" />
                        </button>
                        <div className="text-center">
                          <span className="text-sm font-light text-darkGray">
                            {travelerConfig.children} Child
                          </span>
                          <div className="text-smokyGray text-[10px] font-light">
                            Ages 2-11
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            const total = travelerConfig.adults + travelerConfig.children;
                            if (total < 9)
                              handleTravelerChange(
                                "children",
                                travelerConfig.children + 1
                              );
                          }}
                          disabled={
                            travelerConfig.adults + travelerConfig.children >= 9 ||
                            travelerConfig.children >= 6
                          }
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.adults + travelerConfig.children >= 9 ||
                            travelerConfig.children >= 6
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Increase children"
                        >
                          <Plus className="w-3 h-3" />
                        </button>
                      </div>
                      <ButtonCom
                        onClick={handleConfirmTravelers}
                        width="full"
                        size="md"
                        variant="primary"
                        rounded="md"
                        ariaLabel="Confirm traveler selection"
                      >
                        Confirm
                      </ButtonCom>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex w-full md:w-[25%]">
            <ButtonCom
              variant="primary"
              size="md"
              width="full"
              icon="search"
              iconPosition="left"
              onClick={handleChangeSearch}
              type="button"
            >
              Change Search
            </ButtonCom>
          </div>
        </div>
      </div>
      <div className="space-y-6">
        {roomTypes.length === 0 ? (
          <div className="text-center text-gray-600">
            No rooms available for the selected dates.
          </div>
        ) : (
          <>
            <RoomTabs
              roomTypes={roomTypes}
              selectedHeader={selectedHeader}
              setSelectedHeader={setSelectedHeader}
              scrollToRoom={scrollToRoom}
            />
            <div className="flex flex-col md:flex-row gap-4">
              <div className="hidden md:block relative chart-container pb-16 space-y-6 w-full md:w-3/4">
                {roomTypes.map((roomType, roomTypeIndex) => (
                  <div
                    key={roomType.id}
                    id={`room-${roomType.id}`}
                    ref={(el) => (roomRefs.current[roomType.id] = el)}
                    className="space-y-4"
                  >
                    {roomType.configurations.map((config, configIndex) => (
                      <RoomListing
                        key={config.id}
                        roomType={roomType}
                        config={config}
                        configIndex={configIndex}
                        roomTypeIndex={roomTypeIndex}
                        selectedRooms={selectedRooms}
                        handleRoomSelect={handleRoomSelect}
                        travelerConfig={travelerConfig}
                        hotelDescription2={hotelDetails.description2}
                        hotelDetails={hotelDetails}
                      />
                    ))}
                  </div>
                ))}
              </div>
              <TotalPanel
                travelerConfig={travelerConfig}
                displayText={displayText}
                calculateTotalPrice={calculateTotalPrice}
                roomTypes={roomTypes}
                selectedRooms={selectedRooms}
                handleReserve={handleReserve}
                bookingDetails={{
                  hotelDetails: processedHotelDetails,
                  searchCriteria,
                  nights,
                  dateRangeText,
                }}
              />
            </div>
          </>
        )}
      </div>
      {/* Mobile view */}
      <div className="flex flex-col h-screen md:hidden">
        <div className="chart-container overflow-y-auto flex-1 px-6 py-3 md:px-0 md:py-0 space-y-4">
          {roomTypes.map((roomType, roomTypeIndex) => (
            <div
              key={roomType.id}
              id={`room-${roomType.id}`}
              ref={(el) => (roomRefs.current[roomType.id] = el)}
              className="border border-border rounded-[8px] p-4 bg-white shadow-md"
            >
              <h3 className="text-lg font-semibold text-darkBlue">{roomType.type}</h3>
              <p className="text-sm text-gray-600 mt-1">{roomType.description}</p>
              <div className="flex mt-1">
                {Array(travelerConfig.adults)
                  .fill(0)
                  .map((_, i) => (
                    <UserRound key={`adult-${i}`} className="w-5 h-5 mr-1" />
                  ))}
                {travelerConfig.children > 0 &&
                  Array(travelerConfig.children)
                    .fill(0)
                    .map((_, i) => (
                      <UserRound key={`child-${i}`} className="w-4 h-4 mr-1" />
                    ))}
              </div>
              <div className="mt-4 space-y-2">
                {roomType.configurations.map((config, configIndex) => (
                  <div
                    key={config.id}
                    className="border border-border p-3 rounded-[8px] space-y-3"
                  >
                    <div className="flex flex-col justify-between items-start mb-2">
                      <p className="text-sm font-medium">{config.price}</p>
                      <p className="text-xs font-light">{config.priceDetails}</p>
                    </div>
                    <div className="text-xs">{config.nights}</div>
                    <ul className="mt-2 space-y-2">
                      {config.benefits.map((benefit, i) => (
                        <li
                          key={i}
                          className="text-xs text-darkGreen flex items-center gap-1"
                        >
                          <Check size={14} strokeWidth={1} /> {benefit.text}
                          {benefit.condition && (
                            <span className="ml-1 text-[10px] text-darkGreen">
                              {benefit.condition}
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                    {selectedRooms[config.id] > 0 ? (
                      <>
                        <button
                          className="mt-3 w-full bg-darkBlue text-white text-sm py-2 rounded-[8px]"
                          onClick={() =>
                            setShowRoomSelectorId(
                              showRoomSelectorId === config.id ? null : config.id
                            )
                          }
                          aria-label={`Selected ${selectedRooms[config.id]} rooms`}
                        >
                          Selected ({selectedRooms[config.id]}{" "}
                          {selectedRooms[config.id] === 1 ? "Room" : "Rooms"})
                        </button>
                        {showRoomSelectorId === config.id && (
                          <div className="mt-2 border border-border p-3 rounded-[8px] bg-[#f9f9f9] space-y-3">
                            <div className="flex items-center justify-between">
                              <button
                                type="button"
                                onClick={() =>
                                  handleRoomSelect(
                                    roomTypeIndex,
                                    configIndex,
                                    (selectedRooms[config.id] || 0) - 1
                                  )
                                }
                                disabled={(selectedRooms[config.id] || 0) <= 0}
                                className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${(selectedRooms[config.id] || 0) <= 0
                                  ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                                  : ""
                                  }`}
                                aria-label="Decrease room quantity"
                              >
                                <Minus className="w-3 h-3" />
                              </button>
                              <span className="text-sm font-light text-darkGray">
                                {selectedRooms[config.id] || 0} Room
                              </span>
                              <button
                                type="button"
                                onClick={() =>
                                  handleRoomSelect(
                                    roomTypeIndex,
                                    configIndex,
                                    (selectedRooms[config.id] || 0) + 1
                                  )
                                }
                                disabled={
                                  (selectedRooms[config.id] || 0) >= travelerConfig.rooms
                                }
                                className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${(selectedRooms[config.id] || 0) >= travelerConfig.rooms
                                  ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                                  : ""
                                  }`}
                                aria-label="Increase room quantity"
                              >
                                <Plus className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <button
                        className="mt-3 w-full bg-darkBlue text-white text-sm py-2 rounded-[8px]"
                        onClick={() => {
                          handleRoomSelect(roomTypeIndex, configIndex, 1);
                          setShowRoomSelectorId(config.id);
                        }}
                        aria-label="Select room"
                      >
                        Select
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        <div className="sticky bottom-0 bg-offWhite z-30 border-t border-border px-4 py-3">
          <div className="max-h-[200vh] overflow-y-auto space-y-3">
            <div className="space-y-2 shadow-sm">
              {selectedRoomDetails.length === 0 ? (
                <div className="text-xs">No rooms selected</div>
              ) : (
                <>
                  <h3 className="text-xs font-normal">Selected Room</h3>
                  {selectedRoomDetails.map(({ roomTypeName, config, count, roomTypeId }) => (
                    <div key={roomTypeId}>
                      <div
                        className="text-xs font-medium text-darkBlue cursor-pointer hover:underline"
                        onClick={() => scrollToRoom(roomTypeId)}
                      >
                        {roomTypeName} ({count} {count > 1 ? "Rooms" : "Room"})
                      </div>
                    </div>
                  ))}
                </>
              )}
              <div className="text-xs">
                {Object.values(selectedRooms).reduce((sum, count) => sum + count, 0)} Room
                {Object.values(selectedRooms).reduce((sum, count) => sum + count, 0) !== 1 ? "s" : ""} for
              </div>
              <div className="text-xs">{displayText}</div>
              <hr className="border-border" />
              <div className="text-sm font-medium w-full text-end">
                CHF {calculateTotalPrice()}
                {selectedRoomDetails.length > 0 && (
                  <p className="text-xs font-normal">
                    {selectedRoomDetails[0].config.totalTax || "+ Taxes and Charges"}
                  </p>
                )}
              </div>
              <div className="flex justify-between items-center pt-4">
                <ButtonCom
                  variant="primary"
                  size="md"
                  width="full"
                  disabled={selectedRoomDetails.length === 0}
                  onClick={() => {
                    if (selectedRoomDetails.length > 0) {
                      handleReserve(
                        selectedRoomDetails[0].roomTypeName,
                        selectedRoomDetails[0].config,
                        selectedRoomDetails[0].roomTypeCode
                      );
                    }
                  }}
                  ariaLabel="Reserve selected room"
                >
                  Reserve
                </ButtonCom>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}