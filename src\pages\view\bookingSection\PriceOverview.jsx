import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

const PriceOverview = () => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);

  // Generate dates for the calendar grid
  const generateDates = () => {
    const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const startDay = firstDay.getDay();
    const offset = startDay === 0 ? 6 : startDay - 1;
    const gridStartDate = new Date(firstDay);
    gridStartDate.setDate(firstDay.getDate() - offset);

    const dates = [];
    for (let i = 0; i < 42; i++) {
      const date = new Date(gridStartDate);
      date.setDate(gridStartDate.getDate() + i);
      const isCurrentMonth =
        date.getMonth() === currentMonth.getMonth() &&
        date.getFullYear() === currentMonth.getFullYear();
      dates.push({ date, isCurrentMonth });
    }
    return dates;
  };

  // Navigation functions
  const goToPreviousMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(currentMonth.getMonth() - 1);
    setCurrentMonth(newMonth);
    setSelectedDate(null);
  };

  const goToNextMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(currentMonth.getMonth() + 1);
    setCurrentMonth(newMonth);
    setSelectedDate(null);
  };

  const dates = generateDates();

  return (
    <div className="mx-auto max-w-[1140px] p-4 sm:p-6 bg-white border border-border rounded-3xl">
      {/* Calendar Header */}
      <div className="flex items-center mb-4 md:mb-10 justify-end">
        <div className="flex items-center gap-2 sm:hidden">
          <button onClick={goToPreviousMonth} className="p-1 text-smokyGray">
            <ChevronLeft className="w-6 h-12" strokeWidth={2} />
          </button>
          <h2 className="text-lg font-medium text-smokyGray">
            {currentMonth.toLocaleString("default", { month: "long", year: "numeric" })}
          </h2>
          <button onClick={goToNextMonth} className="p-1 text-smokyGray">
            <ChevronRight className="w-6 h-12" strokeWidth={2} />
          </button>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden sm:flex items-center gap-4">
          <button onClick={goToPreviousMonth} className="p-1 text-smokyGray">
            <ChevronLeft className="w-9 h-18" strokeWidth={2} />
          </button>
          <h2 className="text-xl md:text-2xl font-medium text-smokyGray">
            {currentMonth.toLocaleString("default", { month: "long", year: "numeric" })}
          </h2>
          <button onClick={goToNextMonth} className="p-1 text-smokyGray">
            <ChevronRight className="w-9 h-18" strokeWidth={2} />
          </button>
        </div>
      </div>

      {/* Week Days Header */}
      <div className="grid grid-cols-7 gap-1 sm:gap-4 mb-4 sm:mb-6">
        {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
          <div
            key={day}
            className="text-center text-xs md:text-sm font-normal text-smokyGray"
          >
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1 sm:gap-4">
        {dates.map((item, index) => (
          <button
            key={index}
            onClick={() => setSelectedDate(item)}
            disabled={!item.isCurrentMonth} 
            className={`
              relative flex flex-col items-center justify-center 
              w-full aspect-square p-1 md:p-3 rounded-lg md:rounded-3xl border
              ${item.isCurrentMonth ? "border-border" : "border-border/50"}
              ${
                selectedDate?.date.getTime() === item.date.getTime()
                  ? "border-2 border-darkBlue"
                  : "hover:border-gray"
              }
              transition-all disabled:opacity-60 disabled:cursor-not-allowed`}
          >
            {/* Date Number */}
            <div
              className={`absolute top-1 left-1 md:top-3 md:left-3 w-4 h-4 md:w-10 md:h-10 flex items-center justify-center 
              rounded-full text-[6px] md:text-base
              ${
                item.isCurrentMonth
                  ? "bg-darkBlue text-white"
                  : "bg-day text-smokyGray" 
              }
            `}
            >
              {item.date.getDate()}
            </div>

            {/* Price Info */}
            <div className="flex flex-col items-end w-full mt-auto">
              <span className="text-[4px] md:text-xs font-normal text-smokyGray truncate">
                LHR 2345676
              </span>
              <span className="text-[3px] md:text-[10px] font-extralight text-smokyGray">
                Per Person
              </span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default PriceOverview;