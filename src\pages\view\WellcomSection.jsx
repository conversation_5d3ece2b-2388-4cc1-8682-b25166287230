import React, { useState } from 'react';
import { Heart, MapPin, Star } from 'lucide-react';
import { useParams, useLocation, Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import img1 from '../../assets/adventure/HotelImage1.svg';
import img2 from '../../assets/adventure/HotelImage2.svg';
import img3 from '../../assets/adventure/HotelImage3.svg';
import img4 from '../../assets/adventure/HotelImage4.svg';
import img5 from '../../assets/adventure/HotelImage5.svg';
import img6 from '../../assets/explore/img_2.png';
import img7 from '../../assets/explore/img_3.png';
import img8 from '../../assets/explore/img_4.jpg';
import img9 from '../../assets/explore/img_5.jpg';
import img10 from '../../assets/explore/img_6.jpg';
import left from '../../assets/view/hotel/left.png';
import right from '../../assets/view/hotel/right.png';
import ImagePopup from './ImagePopup';
import FacilitiesIcon from './FacilitiesIcon';
import ButtonCom from '../../components/ui/button/ButtonCom';

delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
});

const WellcomSection = ({ hotelDetails, searchCriteria }) => {
  const { hotelId } = useParams();
  const location = useLocation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [showGalleryModal, setShowGalleryModal] = useState(false);
  const [galleryIndex, setGalleryIndex] = useState(0);

  const handleImageClick = () => {
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleNextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % effectiveImages.length);
  };

  const handlePrevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + effectiveImages.length) % effectiveImages.length);
  };

  const handleViewAllPhotos = () => {
    setGalleryIndex(0);
    setShowGalleryModal(true);
  };

  const effectiveSearchCriteria = searchCriteria || location.state?.formData || {};

  const defaultImages = [img1, img2, img3, img4, img5, img6, img7, img8, img9, img10];
  const uniqueImages = (images) => {
    const seen = new Set();
    return images.filter((img) => {
      if (seen.has(img)) return false;
      seen.add(img);
      return true;
    });
  };

  const rawImages = hotelDetails.images?.length > 0 ? hotelDetails.images : defaultImages;
  const effectiveImages = uniqueImages(rawImages);

  const calculateNightsAndTravelers = () => {
    let nights = 1;
    let dateRangeText = 'Select dates';
    let travelersText = '2 Adults';

    try {
      const startDate = new Date(effectiveSearchCriteria.dateRange?.startDate);
      const endDate = new Date(effectiveSearchCriteria.dateRange?.endDate);

      if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
        if (endDate >= startDate) {
          nights = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
          const formatter = new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric',
          });
          dateRangeText = `${formatter.format(startDate)} - ${formatter.format(endDate)}`;
        } else {
          console.warn('Invalid date range: endDate is before startDate');
          dateRangeText = 'Invalid dates';
        }
      }
    } catch (error) {
      console.error('Error calculating nights:', error);
    }

    const adults = effectiveSearchCriteria.adults || 2;
    const children = effectiveSearchCriteria.children || 0;
    travelersText = [
      adults > 0 ? `${adults} ${adults === 1 ? 'Adult' : 'Adults'}` : '',
      children > 0 ? `${children} ${children === 1 ? 'Child' : 'Children'}` : '',
    ]
      .filter(Boolean)
      .join(', ');

    return {
      nights: Math.max(1, nights),
      displayText: `${nights} ${nights === 1 ? 'night' : 'nights'}, ${travelersText}`,
      dateRangeText,
      travelersText,
    };
  };

  const { nights, displayText, dateRangeText, travelersText } = calculateNightsAndTravelers();

  const calculateAvailableRooms = () => {
    if (!hotelDetails.rooms || hotelDetails.rooms.length === 0) return 0;
    return hotelDetails.rooms.reduce((total, roomType) => {
      if (!roomType.rateBases) return total;
      return total + roomType.rateBases.reduce((sum, rate) => sum + (rate.leftToSell || 0), 0);
    }, 0);
  };

  const totalAvailableRooms = calculateAvailableRooms();

  const cheapestRoom = hotelDetails.rooms?.reduce((cheapest, roomType) => {
    if (!roomType.rateBases) return cheapest;
    const cheapestRate = roomType.rateBases.reduce((minRate, rate) => {
      if (!rate.totalCharge || rate.totalCharge >= minRate.totalCharge) return minRate;
      return {
        ...rate,
        roomTypeName: roomType.roomTypeName,
        roomTypeCode: roomType.roomTypeCode || `ROOM_${roomType.id || 0}`,
        allocationDetails: rate.allocationDetails || "",
      };
    }, { totalCharge: Infinity, roomTypeName: null, roomTypeCode: null, allocationDetails: "" });
    if (cheapestRate.totalCharge < cheapest.totalCharge) {
      return cheapestRate;
    }
    return cheapest;
  }, { totalCharge: Infinity, roomTypeName: null, roomTypeCode: null, allocationDetails: "" });

  const cancellationBenefits = cheapestRoom.cancellationRules?.map((rule) => {
    if (rule.type === "free_cancellation" && rule.cancelCharge === 0) {
      return {
        text: "Free Cancellation",
        condition: rule.toDateDetails || "Before check-in",
        included: true,
      };
    } else if (rule.type === "penalty_period" && rule.cancelCharge > 0) {
      return {
        text: `Cancellation CHF ${rule.formattedCancelCharge}`,
        condition: rule.fromDateDetails
          ? `From ${rule.fromDateDetails}${rule.toDateDetails ? ` to ${rule.toDateDetails}` : ''}`
          : "During penalty period",
        included: false,
      };
    } else if (rule.type === "no_show" && rule.noShowPolicy) {
      return {
        text: `No-Show Fee: CHF ${rule.formattedCharge}`,
        condition: rule.fromDateDetails || "On check-in date",
        included: false,
      };
    }
    return null;
  }).filter(Boolean) || [];

  const getStarCount = () => {
    if (hotelDetails.classificationBody) {
      const starsFromName = hotelDetails.classificationBody.classificationName?.match(/\*+/)?.[0]?.length;
      if (starsFromName) return starsFromName;

      const starsFromRating = hotelDetails.classificationBody.rating?.match(/(\d+)\s*Star/i)?.[1];
      if (starsFromRating) return parseInt(starsFromRating, 10);

      const classificationId = hotelDetails.classificationBody.classificationId;
      if (classificationId) {
        switch (classificationId) {
          case 559: return 1;
          case 560: return 2;
          case 561: return 3;
          case 562: return 4;
          case 563: return 5;
          default: return 0;
        }
      }
    }

    if (hotelDetails.classificationCode) {
      switch (hotelDetails.classificationCode) {
        case 559: return 1;
        case 560: return 2;
        case 561: return 3;
        case 562: return 4;
        case 563: return 5;
        default: return 0;
      }
    }

    return 0;
  };

  const getClassificationName = () => {
    if (hotelDetails.classificationBody?.classificationName) {
      return hotelDetails.classificationBody.classificationName.replace(/\*+$/, '').trim();
    }
    if (hotelDetails.classificationBody?.rating) {
      return hotelDetails.classificationBody.rating;
    }
    return hotelDetails.classificationName || 'Unrated';
  };

  const processedHotelDetails = {
    id: hotelDetails.id || hotelId,
    name: hotelDetails.name || 'Unknown Hotel',
    city: hotelDetails.city || 'Unknown City',
    country: hotelDetails.country || 'Unknown Country',
    address: hotelDetails.address || 'Unknown Address',
    description: hotelDetails.description || 'No description available.',
    rating: hotelDetails.rating || 9.2,
    starCount: getStarCount(),
    classificationName: getClassificationName(),
    reviewCount: hotelDetails.reviewCount || 9504,
    cheapestPrice: cheapestRoom.totalCharge !== Infinity ? cheapestRoom.totalCharge.toFixed(2) : 'N/A',
    cheapestRoomType: cheapestRoom.roomTypeName || 'Standard Room',
    images: effectiveImages,
    facilities: hotelDetails.facilities?.length > 0 ? hotelDetails.facilities : [],
    geoLat: hotelDetails.geoLat,
    geoLong: hotelDetails.geoLong,
    isRoomsAvailable: totalAvailableRooms > 0,
  };

  const selectedRoom = {
    roomTypeName: processedHotelDetails.cheapestRoomType,
    totalCharge: parseFloat(processedHotelDetails.cheapestPrice) || 0,
    rateBaseId: cheapestRoom.rateBaseId || 0,
    roomTypeCode: cheapestRoom.roomTypeCode || "ROOM_DEFAULT",
    allocationDetails: cheapestRoom.allocationDetails || "",
    isBookable: cheapestRoom.isBookable !== undefined ? cheapestRoom.isBookable : true,
    benefits: [
      ...(cheapestRoom.rateBaseName ? [{
        text: cheapestRoom.rateBaseName,
        included: true,
      }] : []),
      ...cancellationBenefits,
    ],
    leftToSell: totalAvailableRooms,
    selectedRoomCounts: {},
    cancellationRules: cheapestRoom.cancellationRules || [],
    availableDates: cheapestRoom.availableDates || [],
    rateBaseName: cheapestRoom.rateBaseName || 'Room Only',
    currencyCode: cheapestRoom.currencyCode || 'USD',
    tariffNotes: cheapestRoom.tariffNotes || '',
  };
  console.log('WellcomSection - bookingDetails:', {
  hotelDetails: processedHotelDetails,
  selectedRoom,
});

  const priorityFacilities = [
    'free wifi',
    'air conditioning',
    'swimming pool - outdoor',
    'swimming pool - indoor',
    'spa',
    'gymnasium',
    'restaurant',
    'bar',
  ];

  const getTopFacilities = (facilities, maxCount) => {
    const sortedFacilities = [...facilities].sort((a, b) => {
      const aPriority = priorityFacilities.includes(a.label.toLowerCase()) ? 0 : 1;
      const bPriority = priorityFacilities.includes(b.label.toLowerCase()) ? 0 : 1;
      return aPriority - bPriority;
    });
    return sortedFacilities.slice(0, maxCount);
  };

  return (
    <div className="w-full md:space-y-8 space-y-4 px-6 py-3 md:px-0 md:py-0">
      <div className="">
        <div className="flex flex-col md:flex-row justify-between gap-1 md:gap-0 ">
          <div>
            <div className="flex flex-col items-start gap-2">
              <div className="flex">
                {processedHotelDetails.starCount === 0 ? (
                  <span className="text-sm text-darkBlue font-medium">
                    {processedHotelDetails.classificationName}
                  </span>
                ) : (
                  [...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className="h-5 w-5"
                      fill={index < processedHotelDetails.starCount ? 'orange' : 'none'}
                      stroke={index < processedHotelDetails.starCount ? 'none' : 'orange'}
                    />
                  ))
                )}
              </div>
              <h1 className="text-2xl font-bold text-darkBlue">{processedHotelDetails.name}</h1>
            </div>
            <div className="flex items-center mt-1 space-x-2">
              <MapPin className="h-5 w-5 mr-1" />
              <span className="md:text-lg text-sm">
                {processedHotelDetails.address}, {processedHotelDetails.city}, {processedHotelDetails.country}
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-4 md:items-end md:mt-10 mt-2">
            <div className="flex justify-start md:justify-end gap-2">
              <Heart
                className="h-6 w-6 stroke-darkBlue hover:fill-red-500 cursor-pointer"
                strokeWidth={1}
                onClick={() => console.log('Heart icon clicked')}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="w-full relative">
        <div className="flex flex-col md:flex-row justify-between md:space-x-4 space-y-6 md:space-y-0">
          <div className="flex flex-col w-full md:w-3/4">
            <div className="rounded-[8px] overflow-hidden md:h-[350px] relative">
              <img
                src={effectiveImages[currentIndex]}
                alt={`Hotel view ${currentIndex + 1}`}
                className="w-full h-full object-cover"
                onError={() => console.error(`Failed to load image: ${effectiveImages[currentIndex]}`)}
              />
              <button
                onClick={handlePrevImage}
                className="z-10 absolute top-1/2 left-4 md:w-20 md:h-20 w-16 h-16 rounded-full bg-white/20 transform -translate-y-1/2 flex items-center justify-center"
              >
                <img src={left} alt="Previous" className="md:w-5 w-3 h-10 cursor-pointer" />
              </button>
              <button
                onClick={handleNextImage}
                className="z-10 absolute top-1/2 right-4 md:w-20 md:h-20 w-16 h-16 rounded-full bg-white/20 transform -translate-y-1/2 flex items-center justify-center"
              >
                <img src={right} alt="Next" className="md:w-5 w-3 h-10 cursor-pointer" />
              </button>
            </div>
            <div className="flex gap-1 mt-2 overflow-x-auto w-full scrollbar-hide">
              {effectiveImages.slice(0, 3).map((image, index) => (
                <div
                  key={index}
                  className="md:min-w-[200px] w-full min-w-5 md:h-[120px] h-24 rounded-[8px] overflow-hidden cursor-pointer"
                  onClick={handleViewAllPhotos}
                >
                  <img src={image} alt={`Thumbnail ${index + 1}`} className="w-full h-full object-cover" />
                </div>
              ))}
              {effectiveImages.length > 5 && (
                <div
                  className="md:min-w-[200px] w-full min-w-5 md:h-[120px] h-24 rounded-[8px] overflow-hidden cursor-pointer relative"
                  onClick={handleViewAllPhotos}
                >
                  <img
                    src={effectiveImages[4]}
                    alt={`Thumbnail 5`}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                    <div className="md:w-20 md:h-20 w-16 h-16 rounded-full bg-white/50 flex items-center justify-center">
                      <span className="text-darkBlue text-lg font-medium">
                        +{effectiveImages.length - 5}
                      </span>
                    </div>
                  </div>
                </div>
              )}
              <ImagePopup
                showModal={showModal}
                showGalleryModal={showGalleryModal}
                handleCloseModal={handleCloseModal}
                handlePrevImage={handlePrevImage}
                images={effectiveImages}
                galleryIndex={galleryIndex}
                setShowModal={setShowModal}
                setShowGalleryModal={setShowGalleryModal}
                handleNextImage={handleNextImage}
                processedHotelDetails={processedHotelDetails}
                effectiveSearchCriteria={effectiveSearchCriteria}
                selectedRoom={selectedRoom}
                nights={nights}
                dateRangeText={dateRangeText}
              />
            </div>
          </div>
          <div className="border border-border rounded-[8px] p-4 min-h-[350px] space-y-4 flex flex-col">
            <h2 className="font-semibold text-base">
              Recommended for {travelersText}
            </h2>
            <div className="border-t border-border space-y-3 pt-2 flex-grow">
              {!processedHotelDetails.isRoomsAvailable ? (
                <div className="flex flex-col h-full justify-between">
                  <div className="space-y-4 py-4">
                    <div className="flex items-center justify-center">
                      <div className="h-16 w-16 rounded-full bg-red-50 flex items-center justify-center">
                        <svg viewBox="0 0 24 24" fill="none" className="w-8 h-8 text-red-500">
                          <path
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="text-center space-y-1">
                      <h3 className="font-medium text-darkBlue">No Availability</h3>
                      <p className="text-sm text-gray-600">
                        We don't have any rooms available for your selected dates.
                      </p>
                    </div>
                  </div>
                  <div className="mt-auto">
                    <Link
                      to="/TrendingPackage"
                      state={{ formData: effectiveSearchCriteria, hotelDetails: hotelId, selectedTab: 2 }}
                      className="block w-full bg-darkBlue text-white py-2 rounded-[8px] text-base font-medium text-center hover:bg-darkBlue/90"
                    >
                      Try Different Dates
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col h-full justify-between">
                  <div className="space-y-6">
                    <div className="flex mt-2 justify-between items-center text-sm">
                      <p className="font-normal">
                        1 x{' '}
                        <span className="font-medium text-darkBlue underline">
                          {processedHotelDetails.cheapestRoomType}
                        </span>
                      </p>
                    </div>
                    <div className="mt-2 space-y-1">
                      <div className="text-sm">
                        Price For: <span className="font-medium">{effectiveSearchCriteria.adults || 2} person</span>
                      </div>
                      {/* <div className="text-xs">
                        Beds: <span className="font-medium">{processedHotelDetails.cheapestRoomType.includes('Double') ? '1 Double' : '3 Single'}</span>
                      </div> */}
                    </div>
                    <div className="mt-4 space-y-1">
                      {selectedRoom.benefits.map((benefit, i) => (
                        <div key={i} className="flex items-center gap-2 text-green">
                          <span className="text-xs">✓</span>
                          <span className="text-xs font-semibold">
                            {benefit.text}
                            {benefit.condition && (
                              <span className="text-[10px] font-normal"> {benefit.condition}</span>
                            )}
                          </span>
                        </div>
                      ))}
                      {totalAvailableRooms > 0 ? (
                        <div className="flex items-center gap-2 text-red">
                          <svg viewBox="0 0 24 24" fill="none" className="w-4 h-4">
                            <circle cx="12" cy="12" r="10" stroke="#FF0C18" strokeWidth="2" />
                            <path d="M12 12V4 A8 8 0 0 1 20 12 H12 Z" fill="#FF0C18" />
                          </svg>
                          <span className="text-xs font-medium">
                            Only {totalAvailableRooms} {totalAvailableRooms === 1 ? 'room' : 'rooms'} left
                          </span>
                        </div>
                      ) : (
                        <div className="text-xs font-medium text-red">
                          Sold out
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col">
                    <div className="text-xs justify-end flex items-end" aria-label={`Stay details: ${displayText}`}>
                      {displayText}
                    </div>
                    <div className="flex justify-end items-end">
                      <span className="font-semibold text-lg">
                        CHF {processedHotelDetails.cheapestPrice}
                      </span>
                    </div>
                    <div className="text-xs text-right justify-end items-end flex">+ CHF 4355 Taxes and Charges</div>
                    <div className="flex justify-end mt-3">
                      <Link
                        to="/finaldetails"
                        state={{
                          bookingDetails: {
                            hotelDetails: processedHotelDetails,
                            searchCriteria: effectiveSearchCriteria,
                            selectedRoom,
                            nights,
                            dateRangeText,
                          },
                        }}
                      >
                        <ButtonCom variant="primary" size="md" width="fixed" rounded="md">
                          Reserve
                        </ButtonCom>
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div>
        {processedHotelDetails.facilities.length > 0 && (
          <div className="flex flex-wrap gap-4 justify-between">
            {getTopFacilities(processedHotelDetails.facilities, 12).map(({ label }, index) => (
              <span
                key={index}
                className="border border-border p-2 rounded-[8px] flex items-center space-x-2 text-sm font-light"
              >
                <FacilitiesIcon label={label} className="w-5 h-5 text-darkBlue" />
                <span>{label}</span>
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default WellcomSection;