import React, { useState } from 'react';
import PaymentForm from '../../../components/ui/PaymentForm';
import Visa from '../../../assets/footer/paymentmethod/Visa.svg';
import Master from '../../../assets/footer/paymentmethod/Mastercard.svg';
import Amex from '../../../assets/footer/paymentmethod/Amex.svg';
import Paypal from '../../../assets/footer/paymentmethod/PayPal.svg';
import Googlepay from '../../../assets/footer/paymentmethod/Googlepay.svg';
import Applepay from '../../../assets/footer/paymentmethod/ApplePay.svg';
import VisaLogo from '../../../assets/footer/paymentmethod/Visa-logo.svg';
import MasterLogo from '../../../assets/footer/paymentmethod/Mastercard-logo.svg';
import PaypalLogo from '../../../assets/footer/paymentmethod/PayPal-logo.svg';
import GooglepayLogo from '../../../assets/footer/paymentmethod/Googlepay-logo.svg';
import ApplepayLogo from '../../../assets/footer/paymentmethod/ApplePay-logo.svg';
import TwintLogo from '../../../assets/footer/paymentmethod/Twint-logo.png';
import postfinance from '../../../assets/footer/paymentmethod/postfinance.svg';
import Vector from '../../../assets/footer/paymentmethod/Vector.png';

const PaymentMethods = ({ onChange }) => {
  const [selectedMethod, setSelectedMethod] = useState('');

  const paymentMethods = [
    { src: Visa, alt: 'Visa', value: 'visa' },
    { src: Master, alt: 'MasterCard', value: 'mastercard' },
    { src: Amex, alt: 'Amex', value: 'amex' },
    { src: Paypal, alt: 'PayPal', value: 'paypal' },
    { src: Googlepay, alt: 'Google Pay', value: 'googlepay' },
    { src: Applepay, alt: 'Apple Pay', value: 'applepay' },
    { src: TwintLogo, alt: 'Twint', value: 'twint' },
    { src: postfinance, alt: 'Post Finance', value: 'postfinance' },
    { src: Vector, alt: 'Scan Icon', value: 'scan' },
  ];

  const logoMethods = [
    { src: VisaLogo, alt: 'Visa Logo', value: 'visa' },
    { src: MasterLogo, alt: 'MasterCard Logo', value: 'mastercard' },
    { src: Amex, alt: 'Amex Logo', value: 'amex' },
    { src: PaypalLogo, alt: 'PayPal Logo', value: 'paypal' },
    { src: GooglepayLogo, alt: 'Google Pay Logo', value: 'googlepay' },
    { src: ApplepayLogo, alt: 'Apple Pay Logo', value: 'applepay' },
    { src: TwintLogo, alt: 'Twint Logo', value: 'twint' },
  ];

  const handleChange = ({ paymentMethod }) => {
    setSelectedMethod(paymentMethod);
    onChange(paymentMethod);
  };

  return (
    <PaymentForm
      title="Payment Methods"
      paymentMethods={paymentMethods}
      logoMethods={logoMethods}
      onChange={handleChange}
    />
  );
};

export default PaymentMethods;