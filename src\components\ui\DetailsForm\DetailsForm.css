.stdropdown-container {
  border: none !important;
  outline: none !important; 
}

.stdropdown-input {
  padding: 0 !important; 
  outline: none !important;
}

.stdropdown-menu {
  border: 1px solid #024575 !important;
  border-radius: 15px !important;
  z-index: 100 !important; 
  width: 265px !important;
  left: -14px !important;
}

.stdropdown-item.selected {
  background-color: #024575;
  color: #ffffff !important;
}

.stdropdown-item {
  padding: 6px 22px !important;
}

.stdropdown-item:hover {
  background-color: #dbe8f6 !important
}

.stsearch-box input {
  border: none !important; 
  border-radius: none !important;
  padding: none !important;
  outline: none !important;
}

/* Custom styles for phone code select */
/* .phone-code-select {
  border: none !important;
  background-color: transparent !important; 
  font-size:none !important; 
  color: #000 !important; 
  outline: none !important;
  width: 100px; 
  padding: 0 4px 0 6px; 
  height: 30px; 
  appearance: none; 
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>'); 
  background-repeat: no-repeat;
  background-position: right 8px center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif, "Twemoji Mozilla" !important; 
}

.phone-code-select:focus {
  outline: none !important;
}

.phone-code-select option {
  background-color: #fff;
  color: #000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif, "Twemoji Mozilla" !important; 
} */

/* Remove border from phone number input */
.no-border {
  border: none !important;
  outline: none !important;
}

/* Title dropdown width fix */
.title-dropdown .relative {
  width: 100% !important;
}

/* Phone code container */
.phone-code-container {
  min-width: 90px;
  max-width: 110px;
  
}
