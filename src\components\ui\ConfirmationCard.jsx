import React from 'react';
import { ChevronRight } from 'lucide-react';
import { FaWhatsapp } from 'react-icons/fa6';
import { MdOutlineMailOutline, MdOutlinePhone } from 'react-icons/md';
import { FormControlLabel, Checkbox } from '@mui/material';

const ConfirmationCard = ({
  title = 'Thank You for booking!',
  reservationData = {},
  invoiceAddress = [],
  priceDetails = [],
  agencyInfo = {},
  bookingCompletedAt = 'N/A',
  specificDetails = null,
  voucherDetails = {},
  onChangeAgency = () => { },
  showChangeCheckbox = false,
}) => {
  return (
    <div className="w-full flex justify-center mt-8">
      <div className="flex flex-col w-full max-w-[1140px]">
        <h1 className="text-lg font-medium text-black ml-6 mb-4">{title}</h1>
        <p className="text-black font-light text-sm ml-6 mb-4">
          We are pleased to inform you that your booking was successfully completed at {bookingCompletedAt}
        </p>
        <div className="flex flex-col md:flex-row w-full max-w-[1140px] p-4 space-y-4 md:space-y-0 md:space-x-4 font-inter">
          <div className="flex flex-col space-y-4 w-full max-w-[744px]">
            <div className="p-4 mb-6 border border-darkBlue rounded-2xl">
              <div className="mb-4 flex flex-row items-center space-x-8">
                <label className="text-smokyGray md:text-base text-sm ml-6">Reservation Code</label>
                <div className="flex">
                  <input
                    type="text"
                    value={reservationData.code || 'N/A'}
                    readOnly
                    className="p-2 md:text-base text-sm border border-border rounded-lg w-full text-smokyGray"
                  />
                </div>
              </div>
              <div>
                <label className="text-smokyGray md:text-sm text-xs font-light ml-6">
                  Ticket Must be issued by {reservationData.issueDeadline || 'N/A'}
                </label>
              </div>
            </div>
            <div className="border border-darkBlue rounded-2xl p-6 pb-8">
              <h2 className="text-xl font-normal text-smokyGray mb-6 ml-6">Invoice Address</h2>
              <div className="space-y-6">
                {invoiceAddress.map((item, index) => (
                  <div key={index} className="flex flex-row items-center gap-8 ml-6">
                    <label className="w-40 text-base font-normal text-smokyGray">{item.label}</label>
                    <div className="box-border flex flex-row items-center p-[4px_9px] gap-3 w-60 h-10 border-[0.5px] border-border rounded-xl overflow-x-auto">
                      <span className="text-sm font-light text-smokyGray whitespace-nowrap">{item.value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {/* New Voucher Details Section */}
            <div className="border border-darkBlue rounded-2xl p-6 pb-8">
              <h2 className="text-xl font-normal text-smokyGray mb-6 ml-6">Voucher Details</h2>
              <div className="space-y-4">
                <table className="w-full text-sm text-smokyGray font-arial border border-[#b2b2b2] rounded-t-[3px] bg-[#b2b2b2]">
                  <thead>
                    <tr>
                      <th className="p-2 text-left">VOUCHER DETAILS</th>
                    </tr>
                  </thead>
                </table>
                <table className="w-full text-sm text-smokyGray font-arial border-l border-r border-b border-[#c9c9c9] rounded-b-[3px] p-2">
                  <tbody>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Booking Reference No</td>
                      <td>{voucherDetails.bookingReferenceNo || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Printed On</td>
                      <td>{voucherDetails.printedOn || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Itinerary Number</td>
                      <td>{voucherDetails.itineraryNumber || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Booked By</td>
                      <td>{voucherDetails.bookedBy || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Booking Status</td>
                      <td>{voucherDetails.bookingStatus || 'N/A'}</td>
                    </tr>
                  </tbody>
                </table>
                <hr className="border-t border-dotted border-[#c9c9c9] my-4" />
                <h3 className="text-sm font-bold text-[#b2b2b2] mb-2">SERVICE PROVIDER DETAILS</h3>
                <table className="w-full text-sm text-smokyGray font-arial">
                  <tbody>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Supplier Name</td>
                      <td>{voucherDetails.supplierName || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Service Name</td>
                      <td>{voucherDetails.serviceName || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Address</td>
                      <td>{voucherDetails.address || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Telephone</td>
                      <td>{voucherDetails.telephone || 'N/A'}</td>
                    </tr>
                  </tbody>
                </table>
                <hr className="border-t border-dotted border-[#c9c9c9] my-4" />
                <h3 className="text-sm font-bold text-[#b2b2b2] mb-2">PASSENGER DETAILS</h3>
                <table className="w-full text-sm text-smokyGray font-arial">
                  <tbody>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Passenger Name</td>
                      <td>{voucherDetails.passengerName || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Passenger Nationality</td>
                      <td>{voucherDetails.passengerNationality || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Country of Residence</td>
                      <td>{voucherDetails.countryOfResidence || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Service Type</td>
                      <td>{voucherDetails.serviceType || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">City</td>
                      <td>{voucherDetails.city || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Supplier Reference</td>
                      <td>{voucherDetails.supplierReference || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Check-in</td>
                      <td>{voucherDetails.checkIn || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Check-out</td>
                      <td>{voucherDetails.checkOut || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Room Type</td>
                      <td>{voucherDetails.roomType || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Room Occupancy</td>
                      <td>{voucherDetails.roomOccupancy || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Rate Basis</td>
                      <td>{voucherDetails.rateBasis || 'N/A'}</td>
                    </tr>
                    <tr>
                      <td className="w-1/3 font-bold p-2">Additional Requests</td>
                      <td>{voucherDetails.additionalRequests || 'none'}</td>
                    </tr>
                  </tbody>
                </table>
                <hr className="border-t border-dotted border-[#c9c9c9] my-4" />
                <h3 className="text-sm font-bold text-[#b2b2b2] mb-2">DAILY RATES</h3>
                <table className="w-full text-sm text-smokyGray font-arial border border-[#b2b2b2] rounded-t-[3px] bg-[#b2b2b2]">
                  <thead>
                    <tr>
                      <th className="p-2 text-left">Date</th>
                      <th className="p-2 text-left">Payable Rate</th>
                      <th className="p-2 text-left">Rate Basis</th>
                      <th className="p-2 text-left">Rate Market</th>
                    </tr>
                  </thead>
                </table>
                <table className="w-full text-sm text-smokyGray font-arial border-l border-r border-b border-[#c9c9c9] rounded-b-[3px] p-2">
                  <tbody>
                    {voucherDetails.dailyRates?.map((rate, index) => (
                      <React.Fragment key={index}>
                        <tr>
                          <td className="p-2 font-bold text-[#b2b2b2]">{rate.date}</td>
                          <td className="p-2">{rate.payableRate}</td>
                          <td className="p-2">{rate.rateBasis}</td>
                          <td className="p-2">{rate.rateMarket}</td>
                        </tr>
                        {index < voucherDetails.dailyRates.length - 1 && (
                          <tr>
                            <td colSpan="4">
                              <hr className="border-t border-dotted border-[#c9c9c9] my-1" />
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))}
                    <tr>
                      <td colSpan="4" className="p-2">
                        Rates including all Room taxes and Service charges and may not include City Taxes.
                        <br />
                        Please note that the rates shown above could be average daily rates and they might not reflect the actual daily rates for the room.
                      </td>
                    </tr>
                  </tbody>
                </table>
                <p className="text-sm font-bold mt-4">
                  Total Payable for this Booking: {voucherDetails.totalPayable || 'N/A'} {voucherDetails.currency || 'USD'}
                </p>
                <h3 className="text-sm font-bold text-[#b2b2b2] mt-4">CANCELLATION POLICY</h3>
                <ul className="text-sm text-smokyGray list-disc pl-5">
                  {voucherDetails.cancellationRules?.map((rule, index) => (
                    <li key={index}>
                      {rule.text}
                      {rule.subText && <span className="block pl-4">{rule.subText}</span>}
                    </li>
                  ))}
                </ul>
                <h3 className="text-sm font-bold text-[#b2b2b2] mt-4">TARIFF NOTES</h3>
                <p className="text-sm text-smokyGray">{voucherDetails.tariffNotes || 'N/A'}</p>
              </div>
            </div>
            {specificDetails && <div>{specificDetails}</div>}
            <div className="border border-darkBlue rounded-2xl p-6 mt-4 pb-8">
              <h2 className="text-[20px] font-normal text-smokyGray mb-6 ml-6">Price Details</h2>
              <div className="space-y-[25px]">
                {priceDetails.map((item, index) => (
                  <div key={index} className="flex flex-row items-center gap-8 ml-6">
                    <label className="w-40 text-base font-normal text-smokyGray">{item.label}</label>
                    <div className="box-border flex flex-row items-center p-[4px_9px] gap-3 w-56 h-10 border-[0.5px] border-border rounded-xl">
                      <span className="text-sm font-light text-smokyGray">{item.value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="w-full max-w-[358px] mx-auto">
            <div className="w-full border border-darkBlue rounded-2xl">
              <div className="w-full h-24 bg-offWhite rounded-t-2xl p-0">
                <h3 className="text-sm font-medium text-darkBlue ml-6 pt-4">Helpdesk</h3>
                <p className="text-xs text-darkBlue font-light mt-2 ml-6">
                  If you have any question regarding booking process and your reservation, please feel free to call us.
                </p>
              </div>
              <div className="px-6">
                <h2 className="text-base font-normal mb-4 mt-8 ml-6">Your Responsible Travel wykonawcy</h2>
                <div className="text-sm font-light text-smokyGray ml-10">
                  <p>{agencyInfo.name || 'N/A'}</p>
                  <p>{agencyInfo.address || 'N/A'}</p>
                  <p className="mb-2">{agencyInfo.city || 'N/A'}</p>
                  <div className="mb-4 mt-4">
                    <p className="flex items-center">
                      <MdOutlinePhone className="mr-2" />
                      <a href={`tel:${agencyInfo.phone}`} className="text-sm font-medium text-darkBlue">
                        {agencyInfo.phone || 'N/A'}
                      </a>
                    </p>
                    <p className="flex items-center">
                      <FaWhatsapp className="mr-2" />
                      <a href={`tel:${agencyInfo.phone}`} className="text-sm font-medium text-darkBlue">
                        {agencyInfo.phone || 'N/A'}
                      </a>
                    </p>
                    <p className="flex items-center">
                      <MdOutlineMailOutline className="mr-2" />
                      <a href={`mailto:${agencyInfo.email}`} className="text-sm font-medium text-darkBlue">
                        {agencyInfo.email || 'N/A'}
                      </a>
                    </p>
                  </div>
                  <p className="font-normal text-black">{agencyInfo.description || 'N/A'}</p>
                  <p className="mb-4">{agencyInfo.subDescription || 'N/A'}</p>
                  <p className="font-normal text-sm text-black mb-2">Opening Hours</p>
                  <p className="hover:text-green">{agencyInfo.openingHours?.current || 'N/A'}</p>
                  <p className="hover:text-green">{agencyInfo.openingHours?.closing || 'N/A'}</p>
                  <p className="hover:text-green">{agencyInfo.openingHours?.nextDay || 'N/A'}</p>
                </div>
                <div className="flex items-center justify-end mt-4 mb-4">
                  <button className="flex items-center text-darkBlue text-base font-extralight" onClick={onChangeAgency}>
                    <span>Change</span>
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationCard;