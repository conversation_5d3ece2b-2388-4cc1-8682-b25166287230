import React from 'react';
import DetailsForm from '../../../components/ui/DetailsForm/DetailsForm';

const Persondetails = ({ formikProps, passengerCount }) => {
  const { values, errors, touched, handleChange, setFieldValue } = formikProps;

  return (
    <div className="space-y-6">
      {values.passengers.map((_, index) => (
        <div key={index} className="">
          <DetailsForm
            title={`Guest ${index + 1} Details${index === 0 ? ' (Main Guest)' : ''}`}
            showFields={{
              firstName: true,
              lastName: true,
              title: true,
              phoneNumber: true,
              email: true,
              nationality: index === 0,
              streetName: index === 0,
              houseNumber: index === 0,
              postalCode: index === 0,
              country: index === 0,
              state: index === 0,
              city: index === 0,
              termsChecked: index === 0,
              bookingFor: index === 0,
              travelPurpose: index === 0,
            }}
            showTermsCheckbox={index === 0}
            showBookingFor={index === 0}
            showTravelPurpose={index === 0}
            formikProps={{
              values: values.passengers[index],
              errors: errors.passengers?.[index] || {},
              touched: touched.passengers?.[index] || {},
              handleChange,
              setFieldValue,
              passengerIndex: index,
            }}
          />
          {/* Error Summary */}
          {touched.passengers?.[index] && errors.passengers?.[index] && (
            <div className="mt-4 text-red text-sm bg-red/15 p-4 rounded-lg">
              <p className="font-semibold">Please correct the following errors:</p>
              <ul className="list-disc pl-5 mt-2">
                {Object.entries(errors.passengers[index]).map(([field, error]) => (
                  <li key={field}>
                    {field === 'nationalityCode'
                      ? 'Nationality'
                      : field === 'countryCode'
                        ? 'Country'
                        : field.charAt(0).toUpperCase() + field.slice(1)}: {error}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default Persondetails;