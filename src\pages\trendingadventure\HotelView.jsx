import React, { useState } from 'react';
import { Star, Check, ChevronRight } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import HotelImage1 from '../../assets/adventure/HotelImage1.svg';
import HotelImage2 from '../../assets/adventure/HotelImage2.svg';
import HotelImage3 from '../../assets/adventure/HotelImage3.svg';
import MapModal from './MapPopup';
import { calculateDistance } from './distanceUtils';
import ButtonCom from '../../components/ui/button/ButtonCom';

const cityCenterCoordinates = {
  'new york': { lat: 40.7128, lng: -74.0060 },
  london: { lat: 51.5074, lng: -0.1278 },
  paris: { lat: 48.8566, lng: 2.3522 },
  rome: { lat: 41.9028, lng: 12.4964 },
  dubai: { lat: 25.2048, lng: 55.2708 },
  bangkok: { lat: 13.7563, lng: 100.5018 },
  singapore: { lat: 1.3521, lng: 103.8198 },
  tokyo: { lat: 35.6895, lng: 139.6917 },
  barcelona: { lat: 41.3851, lng: 2.1734 },
  istanbul: { lat: 41.0082, lng: 28.9784 },
  hong_kong: { lat: 22.3193, lng: 114.1694 },
  las_vegas: { lat: 36.1699, lng: -115.1398 },
  sydney: { lat: -33.8688, lng: 151.2093 },
  bali: { lat: -8.3405, lng: 115.0920 },
  cancun: { lat: 21.1619, lng: -86.8515 },
  amsterdam: { lat: 52.3676, lng: 4.9041 },
  venice: { lat: 45.4408, lng: 12.3155 },
  san_francisco: { lat: 37.7749, lng: -122.4194 },
  beijing: { lat: 39.9042, lng: 116.4074 },
  los_angeles: { lat: 34.0522, lng: -118.2437 }
};


const classificationMapping = {
  559: { stars: 1, label: '1 Star' },
  560: { stars: 2, label: '2 Stars' },
  561: { stars: 3, label: '3 Stars' },
  562: { stars: 4, label: '4 Stars' },
  563: { stars: 5, label: '5 Stars' },
};

const getStarCount = (classificationCode) => {
  return classificationMapping[classificationCode]?.stars || 0;
};

const HotelView = ({ hotels }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const searchCriteria = location.state?.formData || {};
  const [selectedHotel, setSelectedHotel] = useState(null);

  // Calculate nights and travelers
  const calculateNightsAndTravelers = () => {
    let nights = 1;
    let dateRangeText = 'Select dates';

    try {
      const startDate = new Date(searchCriteria.dateRange?.startDate);
      const endDate = new Date(searchCriteria.dateRange?.endDate);

      if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
        if (endDate >= startDate) {
          nights = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
          const formatter = new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: '2-digit',
          });
          dateRangeText = `${formatter.format(startDate)} - ${formatter.format(endDate)}`;
        } else {
          console.warn('Invalid date range: endDate is before startDate');
          dateRangeText = 'Invalid dates';
        }
      } else {
        console.warn('Missing or invalid dateRange in searchCriteria');
      }
    } catch (error) {
      console.error('Error calculating nights:', error);
    }

    const adults = searchCriteria.adults || 2;
    const children = searchCriteria.children || 0;
    const travelersText = [
      adults > 0 ? `${adults} ${adults === 1 ? 'Adult' : 'Adults'}` : '',
      children > 0 ? `${children} ${children === 1 ? 'Child' : 'Children'}` : '',
    ]
      .filter(Boolean)
      .join(', ');

    return {
      nights: Math.max(1, nights),
      displayText: `${nights} ${nights === 1 ? 'night' : 'nights'}, ${travelersText}`,
    };
  };

  const { nights, displayText } = calculateNightsAndTravelers();

  // Process hotels
  const processHotels = (hotelsList) => {
    if (!Array.isArray(hotelsList)) {
      console.warn('Hotels list is not an array:', hotelsList);
      return [];
    }

    return hotelsList.map((hotel) => {
      try {
        const cityName = hotel.cityData?.cityName || hotel.city || 'Unknown';
        const cityCenter =
          cityCenterCoordinates[cityName] ||
          cityCenterCoordinates[cityName.toLowerCase()] ||
          cityCenterCoordinates[cityName.toUpperCase()] ||
          null;
        let distanceFromCenter = 'Unknown distance';

        if (cityCenter && hotel.geoLat && hotel.geoLong) {
          const hotelLat = parseFloat(hotel.geoLat);
          const hotelLng = parseFloat(hotel.geoLong);
          if (!isNaN(hotelLat) && !isNaN(hotelLng)) {
            distanceFromCenter = `${calculateDistance(
              cityCenter.lat,
              cityCenter.lng,
              hotelLat,
              hotelLng
            ).toFixed(1)} km from center`;
          }
        }

        return {
          id: hotel.hotelId || 0,
          name: hotel.hotelName || 'Unknown Hotel',
          city: hotel.cityData?.cityName || hotel.city || 'Unknown City',
          country: hotel.cityData?.countryData?.countryName || 'Unknown Country',
          cheapestPrice:
            hotel.cheapestRoomCharge != null && typeof hotel.cheapestRoomCharge === 'number'
              ? hotel.cheapestRoomCharge.toFixed(2)
              : 'N/A',
          cheapestRoomType: hotel.cheapestRoomTypeName || 'Standard Room',
          rating: hotel.rating ? (hotel.rating / 100).toFixed(1) : 'N/A',
          starCount: getStarCount(hotel.classificationCode),
          classificationName: hotel.classificationName || 'Unrated',
          reviewCount: hotel.reviewCount || 'No reviews',
          distanceFromCenter,
          image:
            hotel.thumbUrl && hotel.thumbUrl !== ''
              ? hotel.thumbUrl
              : [HotelImage1, HotelImage2, HotelImage3][Math.floor(Math.random() * 3)],
          geoLat: hotel.geoLat || null,
          geoLong: hotel.geoLong || null,
          hotelStreetAddress: hotel.hotelStreetAddress || 'Unknown Address',
        };
      } catch (error) {
        console.error('Error processing hotel:', hotel, error);
        return null;
      }
    }).filter(Boolean);
  };

  const processedHotels = processHotels(hotels);

  console.log('Processed hotels:', processedHotels);

  const handleSeeAvailability = (hotel) => {
    navigate(`/hotel-and-flight/${hotel.id}`, {
      state: { formData: searchCriteria, hotelDetails: hotel, selectedTab: 2 },
    });
  };

  const handleShowMap = (hotel) => {
    setSelectedHotel(hotel);
  };

  const closeMapModal = () => {
    setSelectedHotel(null);
  };

  return (
    <div className="flex flex-col gap-6 rounded-[8px]">
      {processedHotels.length === 0 ? (
        <div className="text-center text-darkBlue">No hotels available</div>
      ) : (
        processedHotels.map((hotel) => (
          <div
            key={hotel.id}
            className="border border-border rounded-[8px] shadow-sm overflow-hidden flex flex-col sm:flex-row"
          >
            <div className="w-full h-[220px] sm:w-1/3 p-3 rounded-[8px]">
              <img
                src={hotel.image}
                alt={`Hotel ${hotel.id}`}
                className="w-full h-48 sm:h-full object-cover rounded-[8px]"
              />
            </div>
            <div className="w-full md:w-2/3 px-4 md:py-4 pb-4 md:pb-0 flex flex-col justify-between">
              <div className="flex flex-col lg:flex-row justify-between">
                <div className="md:w-2/3 space-y-8 md:space-y-2">
                  {/* Hotel Title & Star Rating */}
                  <div>
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={() => handleSeeAvailability(hotel)}
                    >
                      <h3 className="text-lg font-semibold text-darkBlue whitespace-nowrap overflow-hidden text-ellipsis max-w-[80%]">
                        {hotel.name}
                      </h3>

                      {hotel.starCount === 0 ? (
                        <span className="text-xs font-medium text-darkBlue ml-2">
                          {hotel.classificationName}
                        </span>
                      ) : (
                        <div className="flex ml-2 shrink-0">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className="w-4 h-4 text-orange"
                              fill={i < hotel.starCount ? 'currentColor' : 'none'}
                              stroke={i < hotel.starCount ? 'none' : 'currentColor'}
                            />
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Address & City */}
                    <p className="text-xs font-medium text-darkBlue">
                      {hotel.hotelStreetAddress}
                    </p>

                    <p className="text-xs text-darkBlue space-x-1">
                      <span className="underline cursor-pointer">{hotel.city}</span>{' '}
                      <span
                        className="underline cursor-pointer"
                        onClick={() => handleShowMap(hotel)}
                      >
                        Show on map
                      </span>{' '}
                      <span className="text-black">{hotel.distanceFromCenter}</span>
                    </p>
                  </div>

                  {/* Cheapest Room Type + Features */}
                  <div>
                    <p className="text-sm font-semibold md:mt-8">
                      {hotel.cheapestRoomType}
                    </p>

                    <div className="mt-2 space-y-1">
                      {[
                        'Free Cancellation',
                        'Penalty Period',
                        'No Show',
                      ].map((label, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Check className="w-4 h-4 text-darkGreen" />
                          <span className="text-xs font-semibold text-darkGreen">{label}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="lg:w-1/3 flex flex-col justify-between mt-4 lg:mt-16">
                  <div className="flex flex-col items-end">
                    <p
                      className="text-[10px] text-black"
                      aria-label={`Stay details: ${displayText}`}
                    >
                      {displayText}
                    </p>
                    <p className="text-base font-bold">
                      {hotel.cheapestPrice === 'N/A'
                        ? 'Price Unavailable'
                        : `CHF ${hotel.cheapestPrice}`}
                    </p>
                    <p className="text-xs text-black">+ Taxes and Charges</p>
                  </div>
                  <div className="self-end">
                    <ButtonCom variant="primary" size="sm" width="auto" icon="chevronRight" iconPosition="right" onClick={() => handleSeeAvailability(hotel)}>
                      See Availability
                    </ButtonCom>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))
      )}
      <MapModal
        isOpen={!!selectedHotel}
        onClose={closeMapModal}
        hotel={selectedHotel || {}}
      />
    </div>
  );
};

export default HotelView;