import React, { useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchHotelDetails, fetchRoomAvailability } from '../../store/hotelSlice';
import WellcomSection from './WellcomSection';
import Booking from './bookingSection/Booking';
import SearchForm2 from '../home/<USER>/SearchForm2';

const HotelAndFlightView = () => {
  const { hotelId } = useParams();
  const location = useLocation();
  const dispatch = useDispatch();
  const searchCriteria = location.state?.formData || JSON.parse(localStorage.getItem('searchCriteria') || '{}');

  const {
    hotelDetails,
    roomData,
    hotelDetailsLoading,
    roomLoading,
    hotelDetailsError,
    roomError,
  } = useSelector((state) => state.hotels);

  if (!searchCriteria.dateRange?.startDate || !searchCriteria.dateRange?.endDate) {
    return (
      <div className="text-center text-red-500">
        Please select valid check-in and check-out dates.
        <Link to="/" className="underline ml-2">Return to Search</Link>
      </div>
    );
  }

  const requestBody = {
    fromDate: searchCriteria.dateRange?.startDate?.toISOString().split('T')[0],
    toDate: searchCriteria.dateRange?.endDate?.toISOString().split('T')[0],
    currencyCode: '520',
    ratingValue: -1,
    minPriceAmount: 0,
    maxPriceAmount: 100000,
    roomCount: searchCriteria.rooms || 1,
    hotelId: parseInt(hotelId),
    roomRequests: [
      {
        adultsCount: searchCriteria.adults || 2,
        childCount: searchCriteria.children || 0,
        rateBasisCode: -1,
        passengerNationalityCode: 81,
        passengerCountryOfResidenceCode: 72,
      },
    ],
  };

  useEffect(() => {
    localStorage.setItem('searchCriteria', JSON.stringify(searchCriteria));
  }, [searchCriteria]);

  useEffect(() => {
    dispatch(fetchHotelDetails(hotelId));
    dispatch(fetchRoomAvailability(requestBody));
  }, [dispatch, hotelId, JSON.stringify(requestBody)]);

  if (hotelDetailsLoading || roomLoading) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-offWhite">
        <div className="text-center text-darkBlue">
          <h3 className="text-lg font-semibold">
            Room Availability...
          </h3>
          <p className="text-sm mt-2">Please wait while we process your request.</p>
        </div>
      </div>
    );
  }

  if (hotelDetailsError || roomError) {
    return (
      <div className="text-center text-red">
        Error: {hotelDetailsError || roomError}
      </div>
    );
  }

  if (!hotelDetails || !roomData) {
    return <div className="text-center text-darkBlue">No data available</div>;
  }

  // Extract roomTypes from roomData, handling potential array structure
  const roomTypes = Array.isArray(roomData) && roomData.length > 0 ? roomData[0]?.roomTypes || [] : roomData?.roomTypes || [];
  const isRoomsAvailable = roomTypes.some((roomType) =>
    roomType.rateBases?.some((rate) => rate.leftToSell > 0)
  ) || false;

  // Combine facilities from amenitieResponses, businessResponses, and leisureResponses
  const facilities = [
    ...(hotelDetails?.facilitiesResponse?.amenitieResponses?.map((amenity) => ({
      label: amenity.amenitieItem,
    })) || []),
    ...(hotelDetails?.facilitiesResponse?.businessResponses?.map((business) => ({
      label: business.businessName,
    })) || []),
    ...(hotelDetails?.facilitiesResponse?.leisureResponses?.map((leisure) => ({
      label: leisure.leisureName,
    })) || []),
  ];

  const combinedHotelDetails = {
    id: hotelDetails?.hotelId || hotelId,
    name: hotelDetails?.hotelName || 'Unknown Hotel',
    city: hotelDetails?.cityData?.cityName || 'Unknown City',
    country: hotelDetails?.cityData?.countryData?.countryName || 'Unknown Country',
    address: hotelDetails?.hotelStreetAddress || 'Unknown Address',
    description: hotelDetails?.description1 || 'No description available.',
    description2: hotelDetails?.description2 || 'No additional description available.',
    rating: hotelDetails?.ratingCode ? (hotelDetails.ratingCode / 100).toFixed(1) : 9.2,
    reviewCount: hotelDetails?.reviewCount || 0,
    images: hotelDetails?.hotelImages?.map((img) => img.url) || [],
    geoLat: hotelDetails?.geoLat ? parseFloat(hotelDetails.geoLat) : null,
    geoLong: hotelDetails?.geoLong ? parseFloat(hotelDetails.geoLong) : null,
    classificationBody: hotelDetails?.classificationBody || null,
    classificationCode: hotelDetails?.classificationCode || null,
    facilities,
    rooms: roomTypes,
    hotelPhone: hotelDetails?.hotelPhone || 'N/A',
    checkInTime: hotelDetails?.checkInTime || 'N/A',
    checkOutTime: hotelDetails?.checkOutTime || 'N/A',
    isRoomsAvailable,
  };

  return (
    <div className="flex justify-center md:px-8">
      <div className="w-full md:space-y-12 space-y-4">
        <div className="w-full z-10 flex justify-center p-4">
          <SearchForm2 initialData={searchCriteria} />
        </div>
        <div className='w-full md:max-w-[80%] mx-auto md:space-y-10'>
          <WellcomSection hotelDetails={combinedHotelDetails} searchCriteria={searchCriteria} />
          <Booking hotelDetails={combinedHotelDetails} searchCriteria={searchCriteria} />
        </div>
      </div>
    </div>
  );
};

export default HotelAndFlightView;