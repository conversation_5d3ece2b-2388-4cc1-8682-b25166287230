import React, { useState } from "react";
import OfferFilter from "./OfferFilter";
import AllOfferSearchForm from "./AllOfferSearchForm";
import FlightAndHotelOffer from "./FlightAndHotelOffer";
import HotelFilter from "./HotelFilter";
import EmiratesLogo from "../../../assets/view/Emirates-Logo.svg";
import QatarAirwaysLogo from "../../../assets/view/Qatar_Airways_Logo.svg";
import FlydubaiLogo from "../../../assets/view/Flydubai-Logo.svg";
import EdelweissLogo from "../../../assets/view/Edelweiss-Logo.svg";
import CardAndCity from "../../home/<USER>";

const flightDetails = [
  {
    id: 1,
    nights: 3,
    adults: 2,
    startDate: "16 Oct 2025",
    endDate: "21 Nov 2025",
    price: 160000,
    totalPrice: 320000,
    alternativeFlightAvailable: true,
    flights: [
      {
        name: "QatarAirways",
        logo: QatarAirwaysLogo,
        startTime: "20 : 00 ZRH",
        endTime: "20 : 00 ZRH",
        flightStartDate: "16 Oct 2025",
        flightEndDate: "16 Oct 2025",
        day: "Sat",
        duration: "9h 30m",
        stops: "1 stop at IST",
      },
      {
        name: "Emirates",
        logo: EmiratesLogo,
        startTime: "20 : 00 ZRH",
        endTime: "20 : 00 ZRH",
        flightStartDate: "16 Oct 2025",
        flightEndDate: "16 Oct 2025",
        day: "Sat",
        duration: "9h 30m",
        stops: "Direct",
      },
    ],
  },
  {
    id: 1,
    nights: 3,
    adults: 2,
    startDate: "16 Oct 2025",
    endDate: "21 Nov 2025",
    price: 160000,
    totalPrice: 320000,
    alternativeFlightAvailable: true,
    flights: [
      {
        name: "Flydubai",
        logo: FlydubaiLogo,
        startTime: "20 : 00 ZRH",
        endTime: "20 : 00 ZRH",
        flightStartDate: "16 Oct 2025",
        flightEndDate: "16 Oct 2025",
        day: "Sat",
        duration: "9h 30m",
        stops: "1 stop at IST",
      },
      {
        name: "Edelweiss",
        logo: EdelweissLogo,
        startTime: "20 : 00 ZRH",
        endTime: "20 : 00 ZRH",
        flightStartDate: "16 Oct 2025",
        flightEndDate: "16 Oct 2025",
        day: "Sat",
        duration: "9h 30m",
        stops: "Direct",
      },
    ],
  },
];

const hotelDetails = [
  {
    id: 1,
    nights: 3,
    adults: 2,
    startDate: "16 Oct 2025",
    endDate: "21 Nov 2025",
    price: 160000,
    totalPrice: 320000,
  },
  {
    id: 1,
    nights: 3,
    adults: 2,
    startDate: "16 Oct 2025",
    endDate: "21 Nov 2025",
    price: 160000,
    totalPrice: 320000,
  },
];

const AllOffer1 = () => {
  const [offerTab, setOfferTab] = useState(0);
  const [isFilterVisible, setIsFilterVisible] = useState(false);

  const toggleFilterSidebar = () => setIsFilterVisible(!isFilterVisible);

  const OfferTabContent = ({ tabId }) => {
    switch (tabId) {
      case 0:
        return <FlightAndHotelOffer flightDetails={flightDetails} />;
      case 1:
        return <FlightAndHotelOffer flightDetails={hotelDetails} />;
      default:
        return <FlightAndHotelOffer flightDetails={flightDetails} />;
    }
  };

  return (
    <div className="p-2 md:p-4">
      <div className="flex flex-col space-y-6">
        {/* Filter and Sort Section in Same Row */}
        <div className="flex flex-row justify-between items-center">
          <button
            className="font-semibold text-base mt-2 text-darkBlue cursor-pointer"
            onClick={toggleFilterSidebar}
          >
            Filter
          </button>
          <div className="flex flex-col">
            <label className="text-sm text-darkBlue">Sort By</label>
            <select className="border border-darkBlue text-darkBlue rounded-full px-4 py-1 text-sm">
              <option>Departure Airport</option>
            </select>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row lg:gap-10">
          {/* Sidebar */}
          <div
             className={`fixed inset-0 md:static overflow-y-auto md:mt-4 mt-11 bg-white p-6 md:p-0 z-40 shadow-md md:shadow-none transform ${
              isFilterVisible ? "translate-x-0" : "-translate-x-full"
            } transition-transform duration-300 ease-in-out lg:translate-x-0`}
          >
            <button
              className="md:hidden absolute top-8 right-11 text-gray text-lg"
              onClick={toggleFilterSidebar}
            >
              ✕
            </button>
            {offerTab === 0 && (
              <div className="w-full rounded-3xl space-y-4">
                <OfferFilter />
              </div>
            )}
            {offerTab === 1 && <HotelFilter />}
          </div>

          {/* Card Section */}
          <div className="flex-1 flex flex-col space-y-4">
            <div className="space-y-8">
              <AllOfferSearchForm setSearchStatus={setOfferTab} />
              <div>
                <OfferTabContent tabId={offerTab} />
              </div>
            </div>
          </div>
        </div>
        <CardAndCity title="Recommendation For you." />
      </div>
    </div>
  );
};

export default AllOffer1;