import React, { useEffect, useState } from "react";
import background from "../../assets/loading/background.svg";
import <PERSON>flyLogo from "../../assets/loading/logo.png";
import Image1 from "../../assets/loading/hotel images/img1.jpg";
import Image2 from "../../assets/loading/hotel images/img2.jpg";
import Image3 from "../../assets/loading/hotel images/img4.jpg";
import Icon1 from "../../assets/loading/icon1.svg";
import Icon2 from "../../assets/loading/icon2.svg";

function HotelLoading() {
  const Images = [
    { src: Image1, alt: "Resort" },
    { src: Image2, alt: "<PERSON><PERSON><PERSON>" },
    { src: Image3, alt: "Statue of Liberty" },
  ];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [progressArray, setProgressArray] = useState(
    new Array(Images.length).fill(0)
  );
  const [isFading, setIsFading] = useState(false);
  const intervalTime = 3000;
  const minimumDisplayTime = 2000;

  useEffect(() => {
    // Preload images
    Images.forEach((image) => {
      const img = new Image();
      img.src = image.src;
    });

    const imageInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % Images.length;

        if (nextIndex === 0) {
          setProgressArray(new Array(Images.length).fill(0));
        }

        return nextIndex;
      });
    }, intervalTime);

    const timer = setTimeout(() => {
      setIsFading(true);
    }, minimumDisplayTime);

    return () => {
      clearInterval(imageInterval);
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    let progressInterval = setInterval(() => {
      setProgressArray((prevArray) => {
        const newArr = [...prevArray];
        for (let i = 0; i < currentImageIndex; i++) {
          newArr[i] = 100;
        }
        if (newArr[currentImageIndex] < 100) {
          newArr[currentImageIndex] += 1;
        }
        return newArr;
      });
    }, intervalTime / 100);

    return () => clearInterval(progressInterval);
  }, [currentImageIndex]);

  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % 3);
    }, 800);

    return () => clearInterval(interval);
  }, []);

  const dotOpacities = [0.4, 0.6, 1];

  return (
    <div
      className={`min-h-screen flex items-center bg-white justify-center p-4 w-full ${isFading ? 'fade-out' : ''}`}
      style={{
        backgroundImage: `url(${background})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        position: "fixed",
        top: 0,
        left: 0,
        zIndex: 1000,
      }}
    >
      <div className="max-w-[100%] w-full grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8 overflow-hidden">
        {/* Image Section */}
        <div className="relative w-full h-[300px] md:w-[667px] md:h-[687px] overflow-hidden rounded-3xl">
          {Images.map((image, index) => (
            <img
              key={index}
              src={image.src}
              alt={image.alt}
              className={`w-full h-full object-cover absolute transition-opacity duration-1000 ease-in-out ${currentImageIndex === index
                  ? "opacity-100 scale-100"
                  : "opacity-0 scale-100"
                }`}
            />
          ))}

          {/* Progress Lines */}
          <div className="absolute top-6 left-0 right-0 flex space-x-2 px-4">
            {Images.map((_, index) => (
              <div
                key={index}
                className="flex-1 bg-white/50 rounded-full h-1 overflow-hidden"
              >
                <div
                  className="bg-white h-full transition-all duration-75 ease-linear"
                  style={{ width: `${progressArray[index]}%` }}
                />
              </div>
            ))}
          </div>

          {/* Calendar Overlay - Always Visible */}
          <div className="absolute bottom-6 left-6 flex items-center space-x-4 p-3">
            <img src={Icon1} alt="Image 1" className="w-24 h-24" style={{ opacity: 0.75 }} />
            <img src={Icon2} alt="Image 2" className="w-24 h-24" style={{ opacity: 0.75 }} />
          </div>
        </div>

        {/* Content Section */}
        <div className="p-4 md:p-6 flex flex-col justify-center">
          <div className="flex items-center mb-4 -ml-6">
            <img src={EflyLogo} alt="Efly Logo" className="w-40" />
          </div>
          <h2 className="text-[28px] md:text-[38px] font-thin text-smokyGray font-poppins ml-2 flex items-center gap-2">
            Finding the best hotels for you...
          </h2>
          {/* Navigation Dots */}
          <div className="relative">
            <div className="absolute bottom md:-bottom-20 left-1/2 transform -translate-x-1/2 flex space-x-2 items-center">
              {dotOpacities.map((baseOpacity, index) => {
                const opacity = dotOpacities[(index + activeIndex) % 3];
                return (
                  <div
                    key={index}
                    className="w-3 h-3 rounded-full bg-gray"
                    style={{ opacity }}
                  />
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default HotelLoading;