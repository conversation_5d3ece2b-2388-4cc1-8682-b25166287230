import { ChevronLeft, ChevronRight, X } from "lucide-react";
import React from "react";
import { Link } from "react-router-dom";
const ImagePopup = ({
  showModal,
  showGalleryModal,
  handleCloseModal,
  handlePrevImage,
  images,
  galleryIndex,
  handleThumbnailClickInModal,
  setShowModal,
  setShowGalleryModal,
  handleNextImage,
  handleGalleryImageClick,
  processedHotelDetails,
  effectiveSearchCriteria,
  selectedRoom,
  nights,
  dateRangeText,
}) => {
  return (
    <div>
      {showModal && !showGalleryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 max-w-4xl">
          <div className="relative w-full max-w-2xl">
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-1"
              onClick={handleCloseModal}
            >
              <X size={24} />
            </button>

            <div className="flex items-center justify-between">
              <button
                className="bg-black bg-opacity-50 rounded-full p-2 text-white"
                onClick={handlePrevImage}
              >
                <ChevronLeft size={24} />
              </button>

              <img
                src={images[galleryIndex]}
                alt={`Large view ${galleryIndex + 1}`}
                className="max-h-screen max-w-full object-contain mx-4"
              />

              <button
                className="bg-black bg-opacity-50 rounded-full p-2 text-white"
                onClick={handleNextImage}
              >
                <ChevronRight size={24} />
              </button>
            </div>

            {/* Modal thumbnail navigation */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="flex gap-2 bg-black bg-opacity-50 p-2 rounded-lg">
                {images.map((image, index) => (
                  <div
                    key={index}
                    className={`w-10 h-6 rounded overflow-hidden cursor-pointer ${
                      galleryIndex === index ? "ring-2 ring-white" : ""
                    }`}
                    onClick={() => handleThumbnailClickInModal(index)}
                  >
                    <img
                      src={image}
                      alt={`Modal thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Full Gallery Modal - showing all images in a grid layout */}
      {showGalleryModal && (
        <div className="fixed inset-0 bg-white z-50 overflow-auto">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <button
                  className="mr-2"
                  onClick={() => setShowGalleryModal(false)}
                >
                  <ChevronLeft size={24} />
                </button>
                <h3 className="md:text-base text-sm  font-bold">Gallery</h3>
              </div>
              <h2 className="md:text-base text-sm font-medium">
                {processedHotelDetails.name}
              </h2>
              <Link
                to="/finaldetails"
                state={{
                  bookingDetails: {
                    hotelDetails: processedHotelDetails,
                    searchCriteria: effectiveSearchCriteria,
                    selectedRoom,
                    nights,
                    dateRangeText,
                  },
                }}
              >
                {/* <button
                  className="w-full md:w-36 p-2 rounded-[2px] md:text-base text-sm font-medium bg-darkBlue text-white hover:bg-darkBlue/90"
                  aria-label="Reserve selected room"
                >
                  Reserve
                </button> */}
              </Link>
            </div>

            {/* Grid of all images */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {images.map((image, index) => (
                <div
                  key={index}
                  className="aspect-square relative rounded-md overflow-hidden cursor-pointer"
                  onClick={() => handleGalleryImageClick(index)}
                >
                  {index === 0 && (
                    <div className="bg-green-600 text-white text-xs font-bold px-2 py-1 absolute top-2 left-2 z-12">
                      New photo
                    </div>
                  )}
                  <img
                    src={image}
                    alt={`Gallery image ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Modal for viewing a specific image after clicking in the gallery */}
      {showModal && showGalleryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
          <div className="relative w-full max-w-5xl">
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-1"
              onClick={() => setShowModal(false)}
            >
              <X size={24} />
            </button>

            <div className="flex items-center justify-between">
              <button
                className="bg-black bg-opacity-50 rounded-full p-2 text-white"
                onClick={handlePrevImage}
              >
                <ChevronLeft size={24} />
              </button>

              <img
                src={images[galleryIndex]}
                alt={`Large view ${galleryIndex + 1}`}
                className="max-h-screen max-w-full object-contain mx-4"
              />

              <button
                className="bg-black bg-opacity-50 rounded-full p-2 text-white"
                onClick={handleNextImage}
              >
                <ChevronRight size={24} />
              </button>
            </div>

            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white">
              {galleryIndex + 1} / {images.length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImagePopup;